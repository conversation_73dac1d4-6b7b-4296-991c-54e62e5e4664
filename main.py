from fastapi import FastAPI
import uvicorn
from pydantic import BaseModel
from starlette.middleware.cors import CORSMiddleware

from workflow.graph import generate_nutrition_plan
from workflow.model import PatientInfo

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class LoginReq(BaseModel):
    username: str
    password: str

@app.post("/login")
async def login(req: LoginReq):
    return {"token": "your_token"}

@app.post("/generate")
def generate_plan(patient: PatientInfo):
    return generate_nutrition_plan(patient)

if __name__ == "__main__":
    # 启动 FastAPI 服务
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)