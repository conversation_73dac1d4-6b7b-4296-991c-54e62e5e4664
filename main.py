from fastapi import FastAP<PERSON>, Depends
import uvicorn
from pydantic import BaseModel
from sqlalchemy.orm import Session
from starlette.middleware.cors import CORSMiddleware

from core.auth import create_access_token
from core.err import Err
from db import get_db
from models import User, success, failure
from workflow.graph import generate_nutrition_plan
from workflow.model import PatientInfo

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class LoginReq(BaseModel):
    username: str
    password: str

@app.post('/login', summary='用户登录')
async def login(req: LoginReq, db: Session = Depends(get_db)):
    user = db.query(
        User.username,
        User.password,
    ).where(User.username == req.username).first()
    if user and req.password == user.password:
        return success(data={
            'token': create_access_token({
                'username': user.username,
            }),
            'username': user.username,
        })
    return failure(Err.LoginFailure)

@app.post("/generate")
def generate_plan(patient: PatientInfo):
    return generate_nutrition_plan(patient)

if __name__ == "__main__":
    # 启动 FastAPI 服务
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)