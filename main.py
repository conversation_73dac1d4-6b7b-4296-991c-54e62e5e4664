from fastapi import FastAPI
import uvicorn
from workflow.graph import generate_nutrition_plan
from workflow.model import PatientInfo

app = FastAPI()

app.add_middleware()

@app.api_route("/generate", methods=["POST"])
def generate_plan(patient: PatientInfo):
    return generate_nutrition_plan(patient)

if __name__ == "__main__":
    # 启动 FastAPI 服务
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)