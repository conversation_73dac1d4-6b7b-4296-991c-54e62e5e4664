name: Deploy I-Follow Project

on:
  push: { }
  workflow_dispatch:  # 允许手动触发

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: https://gitea.com/actions/checkout@v3

      - name: Log in to private Docker registry
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login ************:8099 -u "${{ secrets.LOCAL_DOCKER_USERNAME }}" --password-stdin

      - name: Build i-follow-server image
        run: docker build -t ************:8099/i-follow/i-follow-server:latest .

      - name: Build i-follow-web image
        run: docker build -t ************:8099/i-follow/i-follow-web:latest -f demo/v3/Dockerfile ./demo/v3/

      - name: Push i-follow-server image
        run: docker push ************:8099/i-follow/i-follow-server:latest

      - name: Push i-follow-web image
        run: docker push ************:8099/i-follow/i-follow-web:latest

      - name: Debug SSH_USER
        run: echo "SSH_USER is $SSH_USER"
        env:
          SSH_USER: ${{ vars.SSH_USER }}

      - name: Deploy on remote server
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          DOCKER_USERNAME: ${{ secrets.LOCAL_DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
          SSH_USER: ${{ vars.SSH_USER }}
        run: |
          echo "$SSH_PRIVATE_KEY" > key.pem
          chmod 600 key.pem

          ssh -i key.pem -o StrictHostKeyChecking=no gitea-runner@************ -p 22 << EOF
            echo "\$DOCKER_PASSWORD" | docker login ************:8099 -u "\$DOCKER_USERNAME" --password-stdin

            docker pull ************:8099/i-follow/i-follow-server:latest
            docker pull ************:8099/i-follow/i-follow-web:latest

            docker rm -f i-follow-server || true
            docker rm -f i-follow-web || true

            docker run -d -p 5747:5747 --name i-follow-server ************:8099/i-follow/i-follow-server:latest
            docker run -d -p 5746:80 --name i-follow-web ************:8099/i-follow/i-follow-web:latest
          EOF
