FROM python:3.10.16-slim-bookworm

# 拷贝 uv 工具
COPY --from=ghcr.wuer0.top/astral-sh/uv:latest /uv /uvx /bin/

# 设置工作目录
WORKDIR /app

# 拷贝项目文件
COPY . .

ENV UV_INDEX_URL=https://pypi.mirrors.ustc.edu.cn/simple

RUN uv pip install -r requirements.txt --system --index-url $UV_INDEX_URL
EXPOSE 5747

# 使用 Gunicorn + Uvicorn Worker 启动 FastAPI 应用
CMD ["gunicorn", "main:app", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:5747", "-w", "2","--timeout","600"]
