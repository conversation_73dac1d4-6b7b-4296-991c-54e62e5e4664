from sqlalchemy import BIGINT, Column, VARCHAR, INTEGER, BOOLEAN, CHAR, DOUBLE
from sqlalchemy.orm import DeclarativeBase


class Base(DeclarativeBase):
    pass

class User(Base):
    __tablename__ = 'user'
    __table_args__ = {'comment': '用户表'}
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    username = Column(VARCHAR(16), nullable=False, comment='登录名称')
    password = Column(VARCHAR(16), nullable=False, comment='登录密码')
    usage_count = Column(INTEGER, nullable=False, comment='LLM剩余调用次数')

class KBNutrient(Base):
    __table__ = 'kb_nutrient'
    __table_args__ = {'comment': '营养素知识库'}
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    name = Column(VARCHAR(10), nullable=False, comment='营养素名称')
    unit = Column(VARCHAR(10), nullable=False, comment='单位')
    is_core = Column(BOOLEAN, nullable=False, default=False, comment='是否为核心营养素（一日膳食能量需要量）')
    gender = Column(CHAR(1), nullable=False, comment='性别，M：男，F：女，A：男女通用')
    age_min = Column(INTEGER, nullable=False, comment='最小年龄，单位：岁')
    age_max = Column(INTEGER, nullable=False, comment='最大年龄，单位：岁')
    target_energy_ratio = Column(DOUBLE, comment='目标能量占比（优先计算）')
    min_energy_ratio = Column(DOUBLE, comment='最小能量占比')
    max_energy_ratio = Column(DOUBLE, comment='最大能量占比')
    kcal_per_gram = Column(INTEGER, comment='每克千卡占比')
    target_intake_value = Column(DOUBLE, comment='目标摄入值')
    min_intake_value = Column(DOUBLE, comment='最小摄入值')
    max_intake_value = Column(DOUBLE, comment='最大摄入值')
    recommended_food = Column(VARCHAR(16), nullable=False, comment='推荐食物')

