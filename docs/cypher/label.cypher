MERGE (a1:标签分组 {name: '主标签'})

MERGE (a1b1:标签类型 {name: '高血脂症'})
MERGE (a1b1c1:标签 {name: '低危'})
MERGE (a1b1c2:标签 {name: '中危'})
MERGE (a1b1c3:标签 {name: '高危'})
MERGE (a1b1)-[:属于]->(a1)
MERGE (a1b1c1)-[:属于]->(a1b1)
MERGE (a1b1c2)-[:属于]->(a1b1)
MERGE (a1b1c3)-[:属于]->(a1b1)


MERGE (a2:标签分组 {name: '合并症标签'})

MERGE (a2b1:标签类型 {name: '高血压'})
MERGE (a2b1c1:标签 {name: '无高血压'})
MERGE (a2b1c2:标签 {name: '1级高血压'})
MERGE (a2b1c3:标签 {name: '2级高血压'})
MERGE (a2b1c4:标签 {name: '3级高血压'})
MERGE (a2b1)-[:属于]->(a2)
MERGE (a2b1c1)-[:属于]->(a2b1)
MERGE (a2b1c2)-[:属于]->(a2b1)
MERGE (a2b1c3)-[:属于]->(a2b1)
MERGE (a2b1c4)-[:属于]->(a2b1)

MERGE (a2b2:标签类型 {name: '糖尿病'})
MERGE (a2b2c1:标签 {name: '1型糖尿病'})
MERGE (a2b2c2:标签 {name: '2型糖尿病'})
MERGE (a2b2c3:标签 {name: '糖尿病前期'})
MERGE (a2b2c4:标签 {name: '糖尿病伴并发症'})
MERGE (a2b2)-[:属于]->(a2)
MERGE (a2b2c1)-[:属于]->(a2b2)
MERGE (a2b2c2)-[:属于]->(a2b2)
MERGE (a2b2c3)-[:属于]->(a2b2)
MERGE (a2b2c4)-[:属于]->(a2b2)

MERGE (a2b3:标签类型 {name: '高尿酸血症'})
MERGE (a2b3c1:标签 {name: '无高尿酸血症'})
MERGE (a2b3c2:标签 {name: '有高尿酸血症'})
MERGE (a2b3c3:标签 {name: '有痛风'})
MERGE (a2b3)-[:属于]->(a2)
MERGE (a2b3c1)-[:属于]->(a2b3)
MERGE (a2b3c2)-[:属于]->(a2b3)
MERGE (a2b3c3)-[:属于]->(a2b3)

MERGE (a2b4:标签类型 {name: 'ASCVD'})
MERGE (a2b4c1:标签 {name: '无ASCVD'})
MERGE (a2b4c2:标签 {name: 'ASCVD超高危人群'})
MERGE (a2b4c3:标签 {name: 'ASCVD极高危人群'})
MERGE (a2b4)-[:属于]->(a2)
MERGE (a2b4c1)-[:属于]->(a2b4)
MERGE (a2b4c2)-[:属于]->(a2b4)
MERGE (a2b4c3)-[:属于]->(a2b4)

MERGE (a2b5:标签类型 {name: '慢性肾脏病'})
MERGE (a2b5c1:标签 {name: '无慢性肾脏病'})
MERGE (a2b5c2:标签 {name: '慢性肾脏病1-2期'})
MERGE (a2b5c3:标签 {name: '慢性肾脏病3-5期'})
MERGE (a2b5)-[:属于]->(a2)
MERGE (a2b5c1)-[:属于]->(a2b5)
MERGE (a2b5c2)-[:属于]->(a2b5)
MERGE (a2b5c3)-[:属于]->(a2b5)

MERGE (a2b6:标签类型 {name: '动脉斑块'})
MERGE (a2b6c1:标签 {name: '无斑块'})
MERGE (a2b6c2:标签 {name: '有斑块，无动脉狭窄'})
MERGE (a2b6c3:标签 {name: '有斑块，且伴动脉轻微狭窄'})
MERGE (a2b6c4:标签 {name: '有斑块，且伴动脉轻度狭窄'})
MERGE (a2b6c5:标签 {name: '有斑块，且伴动脉中度狭窄'})
MERGE (a2b6c6:标签 {name: '有斑块，且伴动脉重度狭窄'})
MERGE (a2b6)-[:属于]->(a2)
MERGE (a2b6c1)-[:属于]->(a2b6)
MERGE (a2b6c2)-[:属于]->(a2b6)
MERGE (a2b6c3)-[:属于]->(a2b6)
MERGE (a2b6c4)-[:属于]->(a2b6)
MERGE (a2b6c5)-[:属于]->(a2b6)
MERGE (a2b6c6)-[:属于]->(a2b6)

MERGE (a2b7:标签类型 {name: 'BMI'})
MERGE (a2b7c1:标签 {name: '体重过轻'})
MERGE (a2b7c2:标签 {name: '正常'})
MERGE (a2b7c3:标签 {name: '超重'})
MERGE (a2b7c4:标签 {name: '肥胖'})
MERGE (a2b7)-[:属于]->(a2)
MERGE (a2b7c1)-[:属于]->(a2b7)
MERGE (a2b7c2)-[:属于]->(a2b7)
MERGE (a2b7c3)-[:属于]->(a2b7)
MERGE (a2b7c4)-[:属于]->(a2b7)


MERGE (a3:标签分组 {name: '其他标签'})

MERGE (a3b1:标签类型 {name: '月经情况'})
MERGE (a3b1c1:标签 {name: '绝经'})
MERGE (a3b1c2:标签 {name: '月经正常'})
MERGE (a3b1c3:标签 {name: '绝经前期'})
MERGE (a3b1)-[:属于]->(a3)
MERGE (a3b1c1)-[:属于]->(a3b1)
MERGE (a3b1c2)-[:属于]->(a3b1)
MERGE (a3b1c3)-[:属于]->(a3b1)

MERGE (a3b2:标签类型 {name: '性别'})
MERGE (a3b2c1:标签 {name: '男性'})
MERGE (a3b2c2:标签 {name: '女性'})
MERGE (a3b2)-[:属于]->(a3)
MERGE (a3b2c1)-[:属于]->(a3b2)
MERGE (a3b2c2)-[:属于]->(a3b2)

MERGE (a3b3:标签类型 {name: '年龄'})
MERGE (a3b3c1:标签 {name: '40岁以下'})
MERGE (a3b3c2:标签 {name: '41-65岁'})
MERGE (a3b3c3:标签 {name: '65岁以上'})
MERGE (a3b3)-[:属于]->(a3)
MERGE (a3b3c1)-[:属于]->(a3b3)
MERGE (a3b3c2)-[:属于]->(a3b3)
MERGE (a3b3c3)-[:属于]->(a3b3)

MERGE (a3b4:标签类型 {name: '支付因素'})
MERGE (a3b4c1:标签 {name: '无商业保险'})
MERGE (a3b4c2:标签 {name: '有商业保险'})
MERGE (a3b4)-[:属于]->(a3)
MERGE (a3b4c1)-[:属于]->(a3b4)
MERGE (a3b4c2)-[:属于]->(a3b4)

MERGE (a3b5:标签类型 {name: '经济因素'})
MERGE (a3b5c1:标签 {name: '家庭年收入少于10万'})
MERGE (a3b5c2:标签 {name: '家庭年收入10-30万'})
MERGE (a3b5c3:标签 {name: '家庭年收入30-50万'})
MERGE (a3b5c4:标签 {name: '家庭年收入50-100万'})
MERGE (a3b5c5:标签 {name: '家庭年收入100万以上'})
MERGE (a3b5)-[:属于]->(a3)
MERGE (a3b5c1)-[:属于]->(a3b5)
MERGE (a3b5c2)-[:属于]->(a3b5)
MERGE (a3b5c3)-[:属于]->(a3b5)
MERGE (a3b5c4)-[:属于]->(a3b5)
MERGE (a3b5c5)-[:属于]->(a3b5)

MERGE (a3b6:标签类型 {name: '教育水平'})
MERGE (a3b6c1:标签 {name: '初中及以下'})
MERGE (a3b6c2:标签 {name: '高中'})
MERGE (a3b6c3:标签 {name: '大学本科'})
MERGE (a3b6c4:标签 {name: '研究生及以上'})
MERGE (a3b6)-[:属于]->(a3)
MERGE (a3b6c1)-[:属于]->(a3b6)
MERGE (a3b6c2)-[:属于]->(a3b6)
MERGE (a3b6c3)-[:属于]->(a3b6)
MERGE (a3b6c4)-[:属于]->(a3b6)

MERGE (a3b7:标签类型 {name: '药物干预'})
MERGE (a3b7c1:标签 {name: '无药物干预'})
MERGE (a3b7c2:标签 {name: '调脂药物'})
MERGE (a3b7c3:标签 {name: '抗血小板药物'})
MERGE (a3b7c4:标签 {name: '降压药物'})
MERGE (a3b7c5:标签 {name: '降糖药物'})
MERGE (a3b7)-[:属于]->(a3)
MERGE (a3b7c1)-[:属于]->(a3b7)
MERGE (a3b7c2)-[:属于]->(a3b7)
MERGE (a3b7c3)-[:属于]->(a3b7)
MERGE (a3b7c4)-[:属于]->(a3b7)
MERGE (a3b7c5)-[:属于]->(a3b7)

MERGE (a3b8:标签类型 {name: '家族史'})
MERGE (a3b8c1:标签 {name: '无家族史'})
MERGE (a3b8c2:标签 {name: '早发冠心病'})
MERGE (a3b8c3:标签 {name: '糖尿病'})
MERGE (a3b8c4:标签 {name: '高血压'})
MERGE (a3b8c5:标签 {name: '脑血管意外'})
MERGE (a3b8)-[:属于]->(a3)
MERGE (a3b8c1)-[:属于]->(a3b8)
MERGE (a3b8c2)-[:属于]->(a3b8)
MERGE (a3b8c3)-[:属于]->(a3b8)
MERGE (a3b8c4)-[:属于]->(a3b8)
MERGE (a3b8c5)-[:属于]->(a3b8)

MERGE (a3b9:标签类型 {name: '过量饮酒'})
MERGE (a3b9c1:标签 {name: '无饮酒嗜好'})
MERGE (a3b9c2:标签 {name: '无过量饮酒'})
MERGE (a3b9c3:标签 {name: '过量饮酒'})
MERGE (a3b9)-[:属于]->(a3)
MERGE (a3b9c1)-[:属于]->(a3b9)
MERGE (a3b9c2)-[:属于]->(a3b9)
MERGE (a3b9c3)-[:属于]->(a3b9);
