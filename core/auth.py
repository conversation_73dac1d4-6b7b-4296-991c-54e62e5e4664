from datetime import datetime, timedelta, timezone

from fastapi import Request
from jose import jwt, JW<PERSON>rror

from models import response401

SECRET_KEY = '26602683025c89cdb009492619c2779430e288ff373bd71db6a8b04fa8c40231'
ALGORITHM = 'HS256'

EXCLUDE_URLS = ['/docs', '/redoc', '/openapi.json', '/login']



def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=7)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def decode_access_token(token: str):
    try:
        decoded_token = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return decoded_token
    except JWTError as e:
        print('JWTError:', e)
        return None

async def authorize_middleware(request: Request, call_next):
    if request.method.upper() == 'OPTIONS' or request.url.path in EXCLUDE_URLS:
        response = await call_next(request)
        return response
    token = request.headers.get('x-token')
    if token is None:
        return response401()
    payload = decode_access_token(token)
    if payload is None:
        return response401()
    request.state.user = payload
    return await call_next(request)