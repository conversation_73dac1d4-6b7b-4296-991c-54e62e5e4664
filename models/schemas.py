from typing import Any, Optional

from fastapi import status
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel
from starlette.responses import JSONResponse

from core.err import Err


class Response(BaseModel):
    success: bool = True
    code: str = '200'
    message: str = 'ok'
    data: Optional[Any]
    total: int = None
    errors: Any = None


def success(data: Any = None, total: int = None):
    return Response(
        success=True,
        data=data,
        total=total,
    ).dict(exclude_none=True)


def failure(err: Err, errors: Any = None):
    message = errors if err == Err.Custom else err[1]
    return Response(
        success=False,
        code=err[0],
        message=message,
        errors=errors
    ).model_dump(exclude_none=True)


def response400(exc: RequestValidationError):
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.Http400, exc.errors())
    )


def response401():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.Http401)
    )


def response404():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.Http404)
    )


def response500():
    return JSONResponse(
        headers={'Access-Control-Allow-Origin': '*'},
        status_code=status.HTTP_200_OK,
        content=failure(Err.Http500)
    )