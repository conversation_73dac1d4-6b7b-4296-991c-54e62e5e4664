from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

import models

connection_str = f"postgresql+psycopg2://postgres:DWyJu5oWCeLxyT9B@192.168.1.12:5433/ifollow_demo"
engine = create_engine(connection_str)

models.Base.metadata.create_all(engine)

SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()