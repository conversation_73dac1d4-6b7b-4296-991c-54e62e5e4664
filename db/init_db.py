import os
import pandas as pd

import models
from db import engine, get_db, SessionLocal

if __name__ == "__main__":
    models.Base.metadata.create_all(engine)

    session = SessionLocal()

    # user = models.User(username="test", password="dai666", usage_count=1000)
    # session.add(user)

    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    df = pd.read_excel(os.path.join(project_root, "data", "Nutrient.xlsx"))
    for idx, row in df.iterrows():
        nutrient = models.KBNutrient(
            name=row["name"],
            unit=row["unit"],
            is_core=True if row["is_core"] == "TRUE" else False,
            gender=row["gender"],
            age_min=row["age_min"],
            age_max=row["age_max"],
            target_energy_ratio=row["target_energy_ratio"],
            min_energy_ratio=row["min_energy_ratio"],
            max_energy_ratio=row["max_energy_ratio"],
            kcal_per_gram=row["kcal_per_gram"],
            target_intake_value=row["target_intake_value"],
            min_intake_value=row["min_intake_value"],
            max_intake_value=row["max_intake_value"],
            recommended_food=row["recommended_food"]
        )
        session.add(nutrient)

    session.commit()
    session.close()