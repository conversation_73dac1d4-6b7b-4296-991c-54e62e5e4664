from langgraph.constants import START, END
from langgraph.graph import StateGraph

from utils.llm import init_langsmith
from utils.pdf_generator import PDFGenerator
from workflow.nodes.nutrition import nutrition_plan
from workflow.nodes.exercise import exercise_plan
from workflow.nodes.screening import screening_plan
from workflow.model import State, PatientInfo

builder = StateGraph(State)
builder.add_node("nutrition_node", nutrition_plan)
builder.add_node("exercise_node", exercise_plan)
builder.add_node("screening_node", screening_plan)
builder.add_edge(START, "nutrition_node")
builder.add_edge(START, "exercise_node")
builder.add_edge(START, "screening_node")
builder.add_edge("nutrition_node", END)
builder.add_edge("exercise_node", END)
builder.add_edge("screening_node", END)
graph = builder.compile()

def generate_nutrition_plan(patient: PatientInfo, gen_pdf=False):

    plan = graph.invoke({"patient": patient})

    # 生成PDF
    if gen_pdf:
        pdf_generator = PDFGenerator(f"output/健康管理方案_001.pdf")
        pdf_generator.generate_health_plan(
            patient_info=plan["patient"].model_dump(),
            nutrition_metrics=plan["nutrition_metrics"],
            nutrition_plan=plan["nutrition"],
            exercise_plan=plan["exercise"],
            screening_plan=plan["screening"]
        )
    return plan

def main():
    init_langsmith("iFollow")
    plan = generate_nutrition_plan(
        PatientInfo(gender="M", age=68, height=1.71, weight=81.0, pla=1.4, tags=["高脂血症低危", "糖尿病"]))
    print(plan)


if __name__ == "__main__":
    main()
