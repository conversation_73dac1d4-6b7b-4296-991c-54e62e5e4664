from enum import Enum
from typing import TypedDict, Literal, List, Optional

from pydantic import BaseModel, Field


class Tag(Enum):
    HPL_L = "高脂血症低危"
    HPL_M = "高脂血症中危"
    HPL_H = "高脂血症高危"
    DM = "糖尿病"


class PatientInfo(BaseModel):
    gender: Literal["M", "F"] = Field(..., description="性别字段，M：男，F：女")
    age: int = Field(..., gt=0, le=120, description="年龄，必须大于0且小于等于120")
    height: float = Field(..., gt=0, le=3.0, description="身高，单位：米，必须大于0且小于等于3.0米")
    weight: float = Field(..., gt=0, le=500, description="体重，单位：公斤，必须大于0且小于等于500公斤")
    pla: float = Field(1.6, gt=0, le=5.0, description="体力活动水平，默认值为1.6，范围0-5.0")
    tags: List[str] = Field(..., description="患者的慢病类型和具体情况，如高血压、糖尿病、高血脂等")

    def to_natural_language(self) -> str:
        """
        将患者信息转换为自然语言描述，用于传入大模型

        Returns:
            str: 患者信息的自然语言描述`   `
        """
        # 性别转换
        gender_text = "男性" if self.gender == "M" else "女性"

        # 身高转换为厘米显示
        height_cm = int(self.height * 100)

        # 体力活动水平描述
        if self.pla <= 1.2:
            activity_level = "久坐少动：如办公室工作，很少运动"
        elif self.pla <= 1.4:
            activity_level = "轻度活动：如偶尔散步，轻微家务"
        elif self.pla <= 1.6:
            activity_level = "中等活动：如每周2-3次运动"
        elif self.pla <= 1.9:
            activity_level = "重度活动：如每天运动或体力劳动"
        else:
            activity_level = "极重度活动：如专业运动员或重体力劳动"

        # 计算BMI并给出体重状态描述
        bmi = self.weight / (self.height ** 2)
        if bmi < 18.5:
            weight_status = "体重偏轻"
        elif bmi < 24:
            weight_status = "体重正常"
        elif bmi < 28:
            weight_status = "超重"
        else:
            weight_status = "肥胖"

        # 年龄段描述
        if self.age < 18:
            age_group = "青少年"
        elif self.age < 30:
            age_group = "青年"
        elif self.age < 50:
            age_group = "中年"
        elif self.age < 65:
            age_group = "中老年"
        else:
            age_group = "老年"

        # 慢病标签处理
        if self.tags:
            disease_info = f"，患有{', '.join(self.tags)}"
        else:
            disease_info = "，目前无明确慢性疾病"

        # 组合完整描述
        description = (
            f"患者为{self.age}岁{gender_text}（{age_group}），"
            f"身高{height_cm}厘米，体重{self.weight}公斤，"
            f"BMI为{bmi:.1f}（{weight_status}），"
            f"体力活动水平为{self.pla}（{activity_level}）"
            f"{disease_info}。"
        )

        return description

class Nutrient(BaseModel):
    name: str = Field(..., description="营养素名称，如蛋白质、脂肪等")
    amount: str = Field(..., description="推荐摄入量，单位为克（g）")
    ratio: str = Field(..., description="占每日总能量的比例，单位为百分比")

class EnergyNutrient(BaseModel):
    eer: int = Field(..., description="每日能量摄入，单位千卡")
    nutrients: List[Nutrient] = Field(..., description="营养素建议列表")

    def to_natural_language(self) -> str:
        nutrients_text = "**一日营养素建议**\n"
        for nutrient in self.nutrients:
            nutrients_text += f"- {nutrient.name}摄入量为{nutrient.amount}克，占每日总能量的{nutrient.ratio}；\n"
        return f"每日膳食能量摄入范围为{self.eer} kcal，{nutrients_text}"

class MealStructureAdviceFood(BaseModel):
    type: Literal[
        "畜禽肉类", "大豆和坚果", "蛋类", "低血糖生成指数食物", "谷类", "烹调用盐", "烹调用油", "全谷类", "乳制品", "蔬菜", "薯类", "水产品", "水果"] = Field(
        ..., description="食物总类")
    amount: int = Field(..., description="推荐量，单位为克（g）")
    source: str = Field(..., description="具体食物")

class MealStructureAdvice(BaseModel):
    name: Literal["早餐", "上午加餐", "午餐", "下午加餐", "晚餐"] = Field(..., description="餐次名称")
    items: List[MealStructureAdviceFood] = Field(..., description="该餐次的食物建议列表")

class NutritionPlan(BaseModel):
    overview: str = Field(..., description="膳食原则及目标，请尽量细致描述，通俗易懂，每种推荐和避免的食物类型需要列举具体的食物名称，如：三文鱼而非简单例举鱼类。请不要少于2000个字。")
    intake: int = Field(..., description="一日膳食能量需要量，单位千卡")
    meal: List[Nutrient] = Field(..., description="一日三餐营养素建议")
    structure: List[MealStructureAdvice] = Field(..., description="一日三餐膳食结构建议")
    note: List[str] = Field(..., description="其他注意事项")

class ExerciseComponent(BaseModel):
    type: Literal["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"] = Field(..., description="运动类型")
    intensity: Literal["低", "中", "高"] = Field(..., description="运动强度")
    # proportion: str = Field(..., description="占比")
    frequency: str = Field(..., description="运动频率")
    duration: str = Field(..., description="运动时间")
    recommend: str = Field(..., description="运动方式，尽量推荐室内和居家可独自完成的运动以及运动说明、示例参考：优先选择快走、椭圆机、八段锦等")
    guidance: str = Field(..., description="运动方式指导。示例参考：八段锦:预备式:并步站立，双手自然下垂，调整呼吸。两手托大理三焦:吸气上举呼气下落。左右开弓似射雕:吸气推出，呼气收回，左右交替。调理胃单举:吸上举，呼气下落，左右交替。五劳七伤往后瞧:吸气转头，呼气还原，左右交替摇头摆尾去心火:吸气倾斜转头，呼气还原，左右交替。两手攀足固肾腰:吸气前倾触脚，呼气起身。攒拳怒目增力气:吸气击拳，呼气收回，左右交替。背后七百病消:吸“提踵，呼气下落抖动。收势:并步站立，双手自然下垂，调整呼吸。")
    notes: Optional[str] = Field(None, description="注意事项")

class ExercisePlan(BaseModel):
    name: str = Field(..., description="运动处方名称")
    overview: str = Field(..., description="运动处方整体描述，请不要少于500个字")
    goal: str = Field(..., description="训练目标，请不要少于200个字")
    components: List[ExerciseComponent] = Field(..., description="训练组成，至少包含1项运动")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: List[str] = Field(..., description="整体注意事项")

class ScreeningPoint(BaseModel):
    item_name: str = Field(..., description="筛查项目")
    screening_idx: Optional[str] = Field(None, description="筛查指标")
    manage_idx: Optional[str] = Field(None, description="管理目标")
    period: str = Field(..., description="随访周期")
    note: Optional[str] = Field(None, description="注意事项")
    purpose: Optional[str] = Field(None, description="监测意义")

    def to_text(self) -> str:
        text = f"筛查项目:{self.item_name}"
        if self.screening_idx:
            text += f"筛查指标: {self.screening_idx}"
        if self.manage_idx:
            text += f"管理目标: {self.manage_idx}"
        text += f"随访周期: {self.period}"
        if self.note:
            text += f"注意事项: {self.note}"
        return text

class RegularFollowUpItem(BaseModel):
    name: str = Field(..., description="项目名称，如：血脂、肝功能、空腹血糖等，必须使用筛查建议中出现的项目名称，禁止创造")
    period: str = Field(..., description="随访周期")
    goal: Optional[str] = Field(None, description="管理目标，如：空腹血檀＜8.2mmol/L、LDL-C<3.4mmol/L、非HDL-C<4.1mmol/L、TG<1.7mmol/L")
    # note: Optional[str] = Field(None, description="注意事项，如：空腹，检查之前1周要避免剧烈运动。")

class ScreeningPlan(BaseModel):
    overall: str = Field(..., description="方案描述")
    regular_items : List[RegularFollowUpItem] = Field(..., description="定期随访项目")
    regular_note: str = Field(..., description="定期随访项目的整体注意事项")
    as_needed: str = Field(..., description="不适随诊症状。")
    note: str = Field(..., description="温馨提示。")

class State(TypedDict):
    patient: PatientInfo
    nutrition: NutritionPlan
    exercise: ExercisePlan
    screening: ScreeningPlan