from typing import List

from workflow.model import Tag, ScreeningPoint


def get_nutrition_point(tag: str, eer: int, weight: float):
    match tag:
        case Tag.HPL_L.value:
            return [
                "控制油脂摄入量：每日低于25g",
                "优先选择不饱和脂肪酸（如植物油和鱼油）替代动物脂肪和棕榈油：动物脂肪和棕榈油每日低于0g",
                "避免摄入反式脂肪酸：每日低于0g（反式脂肪酸包括油条等煎炸食品、面包等烘烤食品、爆米花等膨化食品及西式糕点等）",
                "限制膳食胆固醇摄入量：每日低于300mg",
                "增加富含ω-3脂肪酸食物的摄入量：增加5%g（富含ω-3脂肪酸食物包括鱼类、坚果等）",
                "摄入蔬菜和水果",
                "摄入富含膳食纤维的食物（富含膳食纤维的食物包括燕麦、大麦、豆类、苹果和柑橘类水果等）",
            ]
        case Tag.HPL_M.value:
            return [
                "控制油脂摄入量：每日低于22.5g",
                "优先选择不饱和脂肪酸（如植物油和鱼油）替代动物脂肪和棕榈油：动物脂肪和棕榈油每日低于0g",
                "避免摄入反式脂肪酸：每日低于0g（反式脂肪酸包括油条等煎炸食品、面包等烘烤食品、爆米花等膨化食品及西式糕点等）",
                "限制膳食胆固醇摄入量：每日低于250mg",
                "增加富含ω-4脂肪酸食物的摄入量：增加10%g（富含ω-3脂肪酸食物包括鱼类、坚果等）",
                "摄入蔬菜和水果",
                "摄入富含膳食纤维的食物（富含膳食纤维的食物包括燕麦、大麦、豆类、苹果和柑橘类水果等）",
            ]
        case Tag.HPL_H.value:
            return [
                "控制油脂摄入量：每日低于20g",
                "优先选择不饱和脂肪酸（如植物油和鱼油）替代动物脂肪和棕榈油：动物脂肪和棕榈油每日低于0g",
                "避免摄入反式脂肪酸：每日低于0g（反式脂肪酸包括油条等煎炸食品、面包等烘烤食品、爆米花等膨化食品及西式糕点等）",
                "限制膳食胆固醇摄入量：每日低于200mg",
                "增加富含ω-5脂肪酸食物的摄入量：增加15%g（富含ω-3脂肪酸食物包括鱼类、坚果等）",
                "摄入蔬菜和水果",
                "摄入富含膳食纤维的食物（富含膳食纤维的食物包括燕麦、大麦、豆类、苹果和柑橘类水果等）",
            ]
        case Tag.DM.value:
            return [
                "减少摄入动物性食物和精制食品",
                "增加摄入天然食物和植物性膳食（天然食物和植物性膳食包括全谷物、蔬菜、豆类、水果、坚果和种子等）",
                "增多摄入植物性食物（植物性食物包括谷类、水果、蔬菜、豆类、果仁等）",
                "食物的加工程度较低，新鲜度较高",
                "主要的食用油是橄榄油",
                f"控制脂肪摄入量：脂肪提供能量 {eer * 0.25:.0f}-{eer * 0.35:.0f} 千卡",
                f"限制饱和脂肪摄入量：饱和脂肪提供能量 {eer * 0.07:.0f}-{eer * 0.08:.0f} 千卡",
                "适量摄入鱼",
                "适量摄入禽肉",
                "适量摄入少量蛋",
                "适量摄入奶酪和酸奶",
                "摄入新鲜水果",
                "减少摄入红肉（红肉包括牛肉、羊肉、猪肉等）",
                f"控制脂肪摄入量：脂肪提供能量 {eer * 0.2:.0f}-{eer * 0.3:.0f} 千卡",
                "增加富含ω-3脂肪酸食物的摄入量：增加5%g",
                "增加富含单不饱和脂肪酸食物的摄入量：增加5%g",
                f"限制饱和脂肪酸摄入量：饱和脂肪酸供能比不超过{eer * 0.12:.0f} 千卡",
                f"限制反式脂肪酸摄入量：反式脂肪酸供能比不超过{eer * 0.02:.0f} 千卡",
                "限制膳食胆固醇摄入量：每日低于300mg",
                f"控制碳水化合物摄入量：脂肪提供能量 {eer * 0.45:.0f}-{eer * 0.6:.0f} 千卡",
                "选择低血糖生成指数的碳水化合物（荞麦、薯粉、藕粉等）",
                "增加摄入非淀粉类蔬菜",
                "增加摄入水果",
                "增加摄入全谷类食物（全谷类食物占总谷类的一半以上）",
                "减少摄入精加工谷类",
                "进餐应定时定量（碳水化合物摄入量与胰岛素剂量和起效时间相匹配）",
                "摄入富含膳食纤维的食物：每日25-36g",
                "摄入富含可溶性膳食纤维的食物：每日10-20g",
                "严格控制蔗糖、果糖制品的摄入（果糖制品包含玉米糖浆等）",
                "适当摄入糖醇和非营养性甜味剂",
                f"控制蛋白质摄入量：蛋白质提供能量 {eer * 0.15:.0f}-{eer * 0.2:.0f} 千卡",
                f"增加摄入优质蛋白：优质蛋白占总蛋白的50%以上",
                f"控制蛋白质摄入量：每日 {0.8 * weight:.1f} 克",
                "限制含盐高的食物摄入量（含盐高的食物包括味精、酱油、盐浸等加工食品、调味酱等）",
                "摄入维生素B12的食物（长期服用二甲双胍者应防止维生素B12缺乏）",
            ]
    return []

def get_exercise_plan(tag: str):
    match tag:
        case Tag.HPL_L.value:
            return """
                **运动处方名称：** 高血脂症低危人群运动处方
                **训练目标：** 降低10年ASCVD风险至<5%，预防心血管危险因素并促进健康
                **训练组成：**
                - 有氧运动，中等强度，每周5次，每次30分钟，运动占比：100%，注意事项：暂无；
                **避免运动：** 暂无
                **整体注意事项：** 以中等强度有氧运动为核心，结合日常活动减少久坐时间。运动前后进行5-10分钟热身与拉伸，保持运动连续性和安全性。建议定期监测血脂、血压及运动耐受性，逐步增加运动量（如延长单次运动时间至45分钟或增加频次至每周7次），同时配合膳食管理（低脂高纤维饮食）以增强降脂效果。
            \n\n"""
        case Tag.HPL_M.value:
            return """
                **运动处方名称：** 高血脂症中危人群运动处方
                **训练目标：** 延缓动脉粥样硬化进展，逆转部分心血管风险因素
                **训练组成：**
                - 有氧运动，中等强度，每周5次，每次30-45分钟，运动占比：60%，注意事项：选择快走、慢跑、骑自行车或游泳等可持续性运动；
                - 抗阻运动，高等强度，每周2-3次，每次20-30分钟，运动占比：40%，注意事项：采用自重训练（如深蹲、俯卧撑、平板支撑），每组8-12次，2-3组/动作。
                **避免运动：**
                - 高强度间歇运动（未医学监护时）
                - 憋气动作（如大重量举重）
                **整体注意事项：** 运动前进行10分钟热身（动态拉伸），运动后10分钟放松（静态拉伸）。抗阻训练需确保动作规范以避免关节损伤，初期建议在专业指导下进行。结合膳食控制（减少饱和脂肪和胆固醇摄入），每3个月监测血脂、颈动脉超声等指标。出现胸闷、头晕等不适时立即停止运动并就医。
            \n\n"""
        case Tag.HPL_H.value:
            return """
                **运动处方名称：** 高血脂症高危人群运动处方
                **训练目标：** 二级预防及高强度干预，降低心血管事件再发风险
                **训练组成：**
                - 有氧运动，中等强度，每周5次，每次30-60分钟，运动占比：50%，注意事项：起始强度为最大心率的50%-60%（需CPET测定），逐步过渡至70%-80%；优先选择快走、椭圆机等低冲击运动
                - 抗阻运动，中等强度，每周2次，每次15-25分钟，运动占比：30%，注意事项：采用固定器械或弹力带训练（如坐姿推胸、腿部推举），避免自由重量训练；每组10-15次，2-3组/动作
                - 柔韧性训练，低等强度，每周3次，每次10-15分钟，运动占比：20%，注意事项：推荐静态拉伸或太极，重点针对肩颈、腰背及下肢肌群
                **避免运动：**
                - 高强度间歇训练
                - 大重量举重
                - 憋气动作
                - 竞技性运动
                **整体注意事项：** 需经心肺运动试验（CPET）和临床评估后制定个体化方案，运动前后进行10分钟热身/放松（动态+静态拉伸）。监测运动时血压波动（收缩压<180mmHg），出现胸痛、气促立即终止运动。建议联合他汀类药物治疗，每3个月复查血脂、颈动脉内膜厚度及运动耐受性。抗阻训练需在康复师指导下进行动作矫正，避免Valsalva动作诱发血压骤升。
            \n\n"""
        case Tag.DM.value:
            return """
                **运动处方名称：** 糖尿病控脂减重运动处方
                **训练目标：** 调控血糖血脂水平并实现体重管理，降低心血管风险
                **训练组成：**
                - 有氧运动，中等强度，每周5-7次，每次30-60分钟，运动占比：60%，注意事项：优先选择快走、椭圆机或水中运动；通过动态血糖监测（CGM）确保运动前血糖>6.0mmol/L，佩戴心率带控制强度（最大心率50%-70%），RPE控制在11-13
                - 抗阻运动，中等强度，每周3次，每次20-30分钟，运动占比：40%，注意事项：采用弹力带、固定器械或自重训练（深蹲/臀桥/平板支撑），每组8-12次，2-3组/动作，避免憋气动作诱发血压骤升
                **避免运动：**
                - 高强度间歇训练
                - 空腹运动
                - 大重量自由重量训练
                - 长时间静止站立
                **整体注意事项：** 运动前需经医生评估（含糖化血红蛋白和足部神经检查）。运动前后进行5-10分钟动态拉伸和呼吸训练，随身携带快速碳水（如葡萄糖片）。建议联合低碳水饮食（每日碳水占比<45%），每周至少3次家庭血糖监测（运动前后重点监测）。出现自主神经病变者需进行运动负荷试验后再调整强度，足部感觉异常者优先选择非负重运动（如游泳/骑车）。
            \n\n"""
    return ""

# 先定义 HPL_M 的值
hpl_m_points = [
    ScreeningPoint(item_name="血脂", screening_idx="胆固醇（TC）", manage_idx=None, period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
    ScreeningPoint(item_name="血脂", screening_idx="甘油三酯（TG）", manage_idx=None, period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
    ScreeningPoint(item_name="血脂", screening_idx="低密度脂蛋白胆固醇（LDL-C）", manage_idx="LDL-C<2.6mmol/L", period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
    ScreeningPoint(item_name="血脂", screening_idx="高密度脂蛋白胆固醇（HDL-C）", manage_idx="非HDL-C<3.4mmol/L", period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="非HDL-C为TC减去HDL-C获得，代表了所有含载脂蛋白B（ApoB）脂蛋白胆固醇的总量，非HDL-C也可作为ASCVD一级和二级预防的干预靶点。"),
    ScreeningPoint(item_name="肝功能", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
    ScreeningPoint(item_name="肾功能", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
    ScreeningPoint(item_name="肌酸激酶", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
]

screening_points = {
    Tag.HPL_L.value: [
        ScreeningPoint(item_name="血脂", screening_idx="胆固醇（TC）", manage_idx=None, period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
        ScreeningPoint(item_name="血脂", screening_idx="甘油三酯（TG）", manage_idx="TG<1.7mmol/L", period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
        ScreeningPoint(item_name="血脂", screening_idx="低密度脂蛋白胆固醇（LDL-C）", manage_idx="LDL-C<3.4mmol/L", period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="降脂治疗中监测的目的：（1）观察是否达到降脂目标值；（2）了解药物的潜在不良反应。根据评估结果，优化药物治疗方案，不建议轻易停药。"),
        ScreeningPoint(item_name="血脂", screening_idx="高密度脂蛋白胆固醇（HDL-C）", manage_idx="非HDL-C<4.1mmol/L", period="4-6周", note="采取静脉血，采血前至少2周内保持日常饮食习惯和体质量稳定，24h内不进行剧烈身体活动，禁食8-12h，坐位休息至少5min。", purpose="非HDL-C为TC减去HDL-C获得，代表了所有含载脂蛋白B（ApoB）脂蛋白胆固醇的总量，非HDL-C也可作为ASCVD一级和二级预防的干预靶点。"),
        ScreeningPoint(item_name="肝功能", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
        ScreeningPoint(item_name="肾功能", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
        ScreeningPoint(item_name="肌酸激酶", screening_idx=None, manage_idx=None, period="4-6周", note=None, purpose=None),
    ],
    Tag.HPL_M.value: hpl_m_points,
    Tag.HPL_H.value: hpl_m_points,  # 复用 HPL_M 的数组值
    Tag.DM.value: [
        ScreeningPoint(item_name="空腹血糖", screening_idx=None, manage_idx=None, period="1个月", note=None, purpose=None),
        ScreeningPoint(item_name="餐后血糖", screening_idx=None, manage_idx=None, period="1个月", note=None, purpose=None),
        ScreeningPoint(item_name="糖化血红蛋白", screening_idx=None, manage_idx="<7.0%", period="3个月", note=None, purpose=None),
        ScreeningPoint(item_name="尿常规", screening_idx=None, manage_idx=None, period="6个月", note=None, purpose=None),
        ScreeningPoint(item_name="尿白蛋白/尿肌酐", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="血肌酐/尿素氮", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="肝功能", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="心电图", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="视力", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="眼底", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="足外观", screening_idx=None, manage_idx=None, period="3个月", note=None, purpose=None),
        ScreeningPoint(item_name="足背动脉搏动", screening_idx=None, manage_idx=None, period="3个月", note=None, purpose=None),
        ScreeningPoint(item_name="神经病变的相关检查", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
        ScreeningPoint(item_name="血脂", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
    ]
}

def get_screening_points(tags: List[str], age: int) -> List[ScreeningPoint]:
    points = []
    if age >= 18:
        for tag in tags:
            points.extend(screening_points[tag])
        if age < 40:
            points.extend([
                ScreeningPoint(item_name="胆固醇（TC）", screening_idx=None, manage_idx=None, period="2年", note=None, purpose=None),
                ScreeningPoint(item_name="甘油三酯（TG）", screening_idx=None, manage_idx=None, period="2年", note=None, purpose=None),
                ScreeningPoint(item_name="低密度脂蛋白胆固醇（LDL-C）", screening_idx=None, manage_idx=None, period="2年", note=None, purpose=None),
                ScreeningPoint(item_name="高密度脂蛋白胆固醇（HDL-C）", screening_idx=None, manage_idx=None, period="2年", note=None, purpose=None),
            ])
        else:
            points.extend([
                ScreeningPoint(item_name="胆固醇（TC）", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
                ScreeningPoint(item_name="甘油三酯（TG）", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
                ScreeningPoint(item_name="低密度脂蛋白胆固醇（LDL-C）", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
                ScreeningPoint(item_name="高密度脂蛋白胆固醇（HDL-C）", screening_idx=None, manage_idx=None, period="12个月", note=None, purpose=None),
            ])
    return points

if __name__ == "__main__":
    print(get_screening_points(["高脂血症高危", "糖尿病"], 53))