from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

from utils.llm import get_llm, init_langsmith
from workflow.knowledge_base import get_screening_points
from workflow.model import State, ScreeningPlan, PatientInfo

template = """
您是一名专业的健康管理师，请根据患者的基本信息、疾病诊断及现有筛查指南，为其制定个性化的健康筛查与随访建议。

📌 注意事项：

若多个权威指南对同一检查项目有不同推荐频率，请以推荐间隔最短者 为准，作为最终筛查建议。生成的项目名称必须是提供的筛查建议出现的项目名称，禁止创造。
所有建议应结合患者的临床情况，确保科学、实用、可操作。
一、方案描述（请按以下格式填写）
请简要说明患者当前疾病状态及其对健康的潜在影响，并概述整体筛查与随访计划。

✅ 示例参考：
鉴于您目前患有高血压及糖尿病，存在较高的心脑血管事件风险。为早期发现并发症并有效控制病情，建议定期进行相关指标监测和靶器官损害评估，请严格按以下时间节点前往医院复查，如有不适请立即就诊。

二、不适随诊症状（请列出）
请列出患者出现哪些症状时需立即就医。

✅ 示例参考：
如出现胸闷、气促、头晕、视物模糊、四肢麻木或水肿等症状，请及时前往医院就诊。

三、温馨提示（请提供鼓励性建议）
提醒患者坚持治疗、定期复查的重要性，增强其健康管理意识。

✅ 示例参考：
慢性疾病的管理需要长期坚持用药和规律生活。请您保持良好的饮食习惯与运动方式，积极配合医生随访，共同守护您的健康。

以下是患者的个人信息：
```
{patient}
```

提供的筛查建议：
```
{screening_points}
```

{format_instructions}
"""

parser = JsonOutputParser(pydantic_object=ScreeningPlan)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "screening_points"],
    partial_variables={"format_instructions": format_instructions}
)

def screening_plan(state: State):
    patient = state["patient"]
    points_text = "\n".join(f"- {point.to_text()}" for point in get_screening_points(patient.tags, patient.age)) + "\n\n"

    chain = (
        {"patient": RunnablePassthrough(), "screening_points": RunnablePassthrough()}
        | prompt
        | get_llm()
        | parser
    )

    screening = chain.invoke({
        "patient": patient,
        "screening_points": points_text
    })

    return {"screening": screening}


def main():
    patient = PatientInfo(gender="M", age=68, height=1.71, weight=81.0, pla=1.4, tags=["高脂血症低危", "糖尿病"])
    screening_plan({"patient": patient})

if __name__ == "__main__":
    init_langsmith("iFollow")
    main()