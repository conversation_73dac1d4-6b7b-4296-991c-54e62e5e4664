from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

from utils.llm import get_llm
from workflow.knowledge_base import get_nutrition_point
from workflow.model import State, NutritionPlan

from utils.nutrition import calc_all, calc_nutrients

template = """
你是一名专业的注册营养师，请根据患者的基本信息、疾病情况和每日膳食能量摄入范围及营养素建议，为其制定个性化的营养方案和一日三餐膳食结构建议。
**注意**
1. 请尽量以拟人口吻输出方案，但不要输出markdown格式。
2. 生成内容需要参考患者基本信息和疾病情况，不能出现与之相悖的内容。
3. 生成内容需要参考患者的每日膳食能量摄入范围及营养素建议，不能出现与之相悖的内容。
4. 生成内容需要参考患者的饮食建议，不能出现与之相悖的内容。每种推荐和避免的食物类型需要列举具体的食物名称，如：三文鱼而非简单例举鱼类。
5. 该方案为营养方案，请不要包含运动建议。
6. **膳食原则及目标：**
- 整体目标：采用以特殊目标维度为主的平衡膳食模式（如地中海饮食、DASH饮食等）。
- 短期目标：改善某项具体指标或症状（如改善血糖、血脂、血压等）；
- 中期目标：达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
- 长期目标：降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
- 食物选择建议：明确适宜摄入的食物类别及应限制或避免的食物类别。推荐的食物类别需要尽量细致给出解释，比如：限制红肉应告知如：牛肉、猪肉、羊肉等。需要列举适合的每一条饮食建议及详细的食物说明。
- 建议烹调方式：推荐适合该疾病的合理烹饪方法（如蒸、煮、炖等，避免油炸、烧烤）。
示例参考：
    1．坚持谷类为主的平衡膳食模式，食物多样，合理搭配。
        本膳食模式的短期目标为改善血脂，中期目标为达到并维持健康体重，长期目标为降低CVD的发生风险。每天的膳食应包括谷薯类、蔬菜水果、畜禽肉蛋奶和豆类食物。平均每天摄入12种以上食物，每周25种以上。
    2．清淡饮食，少盐少油
        培养清淡饮食习惯，少吃高盐和油炸食品。成年人每天摄入食盐不超过5克；亨调油不超过25克。
        建议进餐应定时定量，采用蒸、煮、炖等健康烹调方式，避免油炸、烧烤。
    3．多吃水果、奶类、全谷、大豆，适量吃鱼、禽、蛋、瘦肉
        建议减少摄入胆固醇，如肥肉、奶油、重油小吃（如曲奇、酥饼、麻花、薯片等）。
        建议避免摄入反式脂肪酸，如油条等煎炸食品、面包等烘烤食品、爆米花等膨化食品及西式糕点等。
        建议适量摄入低血糖生成指数食物，如苹果等。
    4．足量饮水
        足量饮水，少量多次。在温和气候条件下，建议每天饮水1700毫升。推荐喝白水或者茶水，少喝或不喝含糖饮料，不用饮料代替白水。

以下是患者的个人信息：
```
{patient}
```

以下是患者的每日膳食能量摄入范围及营养素建议：
```
{nutrient}
```

以下是饮食建议：
```
{diet}
```

{format_instructions}
"""


parser = JsonOutputParser(pydantic_object=NutritionPlan)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "nutrients", "diet"],
    partial_variables={"format_instructions": format_instructions}
)

def nutrition_plan(state: State):
    patient = state["patient"]
    nutrient_metrics = calc_nutrients(patient.gender, patient.age, patient.height, patient.weight, patient.pla)
    diet = ""
    for tag in patient.tags:
        diet += (f"**{tag}饮食建议：**\n" +
                        "\n".join(f"- {p}" for p in get_nutrition_point(tag, nutrient_metrics["eer"], patient.weight)) + "\n\n")
    chain = (
        {"patient": RunnablePassthrough(), "nutrient": RunnablePassthrough(), "diet": RunnablePassthrough()}
        | prompt
        | get_llm()
        | parser
    )
    nutrition = chain.invoke({
        "patient": patient.to_natural_language(),
        "nutrient": format_core_nutrient(nutrient_metrics),
        "diet": diet
    })
    return {
        "nutrition_metrics": nutrient_metrics,
        "nutrition": nutrition
    }

def format_core_nutrient(metrics: dict) -> str:
    nutrients_text = "**一日营养素建议**\n"
    for core in metrics["core"]:
        nutrients_text += f'- {core["name"]}摄入量为{core["amount"]}克，占每日总能量的{core["ratio"]}；\n'
    return f"每日膳食能量摄入范围为{metrics['eer']} kcal，{nutrients_text}"