from typing import Literal, List

from langchain_core.output_parsers import Str<PERSON>utputParser, JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from pydantic import BaseModel, Field

from utils.llm import get_llm
from workflow.knowledge_base import get_exercise_plan
from workflow.model import State, ExercisePlan

template = """
你是一名专业的运动医学医生，请根据患者的基本信息、疾病情况以及给定的运动干预方案，为其制定个性化的运动处方建议。

✅ 要求：
1. 内容必须依据患者的基本信息与疾病情况，不得出现与其相悖的建议；
2. 内容必须参考提供的运动干预方案，不得偏离其原则与核心要求；
3. 运动处方名称需结合疾病特点和干预目标，具备针对性与专业性；
4. 请确保建议内容科学、合理，前后逻辑一致，避免冲突或矛盾。
5. 该方案为运动方案，请不要出现营养相关建议。
6. **整体注意事项：** 关于运动细节的描述，但请不要出现关于饮食、营养的建议。如果有多条事项请用序号进行排列。

以下是患者的基本信息：
```
{patient}
```

以下给定的运动干预方案：
```
{exercise}
```

{format_instructions}
"""



parser = JsonOutputParser(pydantic_object=ExercisePlan)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "exercise"],
    partial_variables={"format_instructions": format_instructions}
)

def exercise_plan(state: State):
    patient = state["patient"]
    exercise_suggestion = ""
    for tag in patient.tags:
        exercise_suggestion += get_exercise_plan(tag)
    chain = (
            {"patient": RunnablePassthrough(), "exercise": RunnablePassthrough()}
            | prompt
            | get_llm()
            | parser
    )
    exercise = chain.invoke({
        "patient": patient.to_natural_language(),
        "exercise": exercise_suggestion
    })
    return {
        "exercise": exercise
    }