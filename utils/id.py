import uuid

def base62_encode(num):
    """
    将数字编码为Base62字符串
    :param num: 要编码的数字
    :return: Base62编码的字符串
    """
    alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
    if num == 0:
        return alphabet[0]
    arr = []
    while num:
        num, rem = divmod(num, 62)
        arr.append(alphabet[rem])
    arr.reverse()
    return ''.join(arr)

def generate_id():
    """生成一个21位的短ID
    
    使用UUID生成唯一标识符，并通过Base62编码压缩为21位字符串。
    生成的ID只包含数字和字母，没有特殊字符。
    
    Returns:
        str: 21位的短ID字符串，如果生成失败则返回None
    """
    try:
        # 生成UUID
        uuid_obj = uuid.uuid4()
        # 转换为整数
        uuid_int = int(uuid_obj.hex, 16)
        # 使用Base62编码
        encoded = base62_encode(uuid_int)
        # 确保长度为21位，不足补0，超过则截取
        return encoded.zfill(21)[:21]
    except Exception as e:
        print(f"生成ID时发生错误: {e}")
        return None

if __name__ == "__main__":
    for i in range(10):
        print(generate_id())

