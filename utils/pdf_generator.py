from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import sys
from typing import List, Any

# 定义颜色
COLORS = {
    'primary': colors.HexColor('#2C3E50'),  # 深蓝灰色
    'secondary': colors.HexColor('#3498DB'),  # 蓝色
    'accent': colors.HexColor('#E74C3C'),  # 红色
    'light': colors.HexColor('#ECF0F1'),  # 浅灰色
    'dark': colors.HexColor('#2C3E50'),  # 深色
    'success': colors.HexColor('#27AE60'),  # 绿色
    'warning': colors.HexColor('#F39C12'),  # 橙色
    'text': colors.HexColor('#2C3E50'),  # 文本颜色
    'background': colors.HexColor('#FFFFFF'),  # 背景色
}

# 注册系统宋体
font_path = "C:\\Windows\\Fonts\\simsun.ttc"
if not os.path.exists(font_path):
    print(f"错误：找不到字体文件 {font_path}")
    sys.exit(1)

try:
    pdfmetrics.registerFont(TTFont("SimSun", font_path))
except Exception as e:
    print(f"错误：注册字体失败 - {str(e)}")
    sys.exit(1)


def _create_table_style(header_bg_color=COLORS['light']) -> TableStyle:
    """创建统一的表格样式"""
    return TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 0.5, COLORS['light']),
        ('BACKGROUND', (0, 0), (-1, 0), header_bg_color),
        ('TEXTCOLOR', (0, 0), (-1, 0), COLORS['primary']),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('PADDING', (0, 0), (-1, -1), 6),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, COLORS['light']]),
        ('LEFTPADDING', (0, 0), (-1, -1), 3),
        ('RIGHTPADDING', (0, 0), (-1, -1), 3),
        ('WORDWRAP', (0, 0), (-1, -1), True),
    ])

def _create_screening_table_style(header_bg_color=COLORS['light']) -> TableStyle:
    """创建筛查表格样式，特定列左对齐"""
    return TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 0.5, COLORS['light']),
        ('BACKGROUND', (0, 0), (-1, 0), header_bg_color),
        ('TEXTCOLOR', (0, 0), (-1, 0), COLORS['primary']),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('PADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 6),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, COLORS['light']]),
        ('WORDWRAP', (0, 0), (-1, -1), True),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (2, 0), (2, -1), 'LEFT'),
        ('ALIGN', (3, 0), (3, -1), 'LEFT'),
        ('LEADING', (0, 0), (-1, -1), 16),
        ('MINIMUMHEIGHT', (0, 0), (-1, -1), 40),
    ])


class PDFGenerator:
    def __init__(self, output_path: str):
        self.output_path = output_path
        self.styles = getSampleStyleSheet()
        self._setup_styles()
        # 计算可用页面宽度（A4宽度减去左右边距）
        self.available_width = A4[0] - 4*cm  # 4cm是左右边距的总和
        
    def _setup_styles(self):
        """设置中文字体样式"""
        # 标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            fontName='SimSun',
            fontSize=24,
            leading=28,
            textColor=COLORS['primary'],
            spaceAfter=20,
            alignment=1,  # 居中
        ))
        
        # 副标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseSubtitle',
            fontName='SimSun',
            fontSize=18,
            leading=22,
            textColor=COLORS['secondary'],
            spaceBefore=15,
            spaceAfter=10,
            borderWidth=0,
            borderColor=COLORS['secondary'],
            borderPadding=5,
            borderRadius=5
        ))
        
        # 正文样式
        self.styles.add(ParagraphStyle(
            name='Chinese',
            fontName='SimSun',
            fontSize=12,
            leading=16,
            textColor=COLORS['text'],
            spaceBefore=6,
            spaceAfter=6,
        ))
        
        # 加粗样式
        self.styles.add(ParagraphStyle(
            name='ChineseBold',
            fontName='SimSun',
            fontSize=14,
            leading=18,
            textColor=COLORS['primary'],
            spaceBefore=8,
            spaceAfter=8,
            fontWeight='bold',
        ))
        
        # 列表项样式
        self.styles.add(ParagraphStyle(
            name='ChineseList',
            fontName='SimSun',
            fontSize=12,
            leading=16,
            textColor=COLORS['text'],
            leftIndent=20,
            spaceBefore=3,
            spaceAfter=3,
        ))

    def _add_paragraphs_from_text(self, text: str, style_name: str = 'Chinese') -> list:
        """将文本按句号拆分为段落，并应用指定样式"""
        elements = []
        if not text:
            return elements

        paragraphs = text.split("。")
        for paragraph in paragraphs:
            stripped = paragraph.strip()
            if stripped:
                elements.append(Paragraph(stripped + "。", self.styles[style_name]))
        return elements

    def _create_section_header(self, title: str) -> list:
        """创建带样式的章节标题"""
        elements = [Paragraph(title, self.styles['ChineseSubtitle']), Spacer(1, 10)]
        return elements

    # 每日非功能营养素摄入建议
    def _mock(self, style_name: str = 'Chinese') -> Table:
        data = [
            ["营养素", "建议量", "建议食物"],
            ["钙", "800mg", "豆腐、低脂奶、芝麻酱、小油菜"],
            ["磷", "680mg", "燕麦、豆类、瘦肉、坚果"],
            ["钾", "2000mg", "西兰花、菠菜、香蕉、南瓜、藜麦"],
            ["钠", "1400mg", "西兰花、菠菜（焯水）、空心菜"],
            ["镁", "310mg", "黑豆、燕麦、菠菜、南瓜子"],
            ["氯", "2200mg", "食盐（间接提供，注意控制）"],
            ["铁", "10mg", "黑木耳、豆类、瘦红肉（适量）"],
            ["碘", "120µg", "碘盐、紫菜、海带（注意用量）"],
            ["锌", "8.5mg", "南瓜子、牛肉、豆腐"],
            ["硒", "60µg", "巴西坚果（每日1粒）、全谷类、鱼"],
            ["铜", "0.8mg", "豆类、坚果、燕麦"],
            ["氟", "1.5mg", "饮用水（含氟）、海鱼"],
            ["铬", "25µg", "西兰花、葡萄、全麦面包"],
            ["锰", "4mg", "燕麦、糙米、坚果"],
            ["维生素A", "640 µg RE", "胡萝卜、南瓜、菠菜、蛋黄"],
            ["维生素D", "15 µg", "三文鱼、鸡蛋、日晒、D强化奶"],
            ["维生素E", "14 mg α-TE", "杏仁、葵花籽、植物油（限量）"],
            ["维生素K", "80 µg", "羽衣甘蓝、菠菜、菜心"],
            ["维生素B1", "1.2 mg", "全麦面包、豆类、瘦猪肉"],
            ["维生素B2", "1.2 mg", "牛奶、鸡蛋、绿叶菜"],
            ["维生素B6", "1.6 mg", "鸡胸肉、鲑鱼、香蕉"],
            ["维生素B12", "2.4 µg", "鸡蛋、鱼、瘦肉"],
            ["烟酸", "12 mg", "花生、蘑菇、瘦禽肉"],
            ["叶酸", "400 µg", "菠菜、芦笋、豆类"],
            ["泛酸", "5 mg", "蘑菇、糙米、坚果"],
            ["生物素", "40 µg", "花椰菜、蛋黄、酵母"],
            ["胆碱", "380 mg", "鸡蛋、豆制品、瘦肉"],
            ["维生素C", "100 mg", "红椒、猕猴桃、西兰花"],
            ["膳食纤维", "25–30 g", "燕麦、红豆、苹果、青菜、亚麻籽"],
        ]
        # 计算列宽，使表格填满页面
        col_widths = [self.available_width * 0.2, self.available_width * 0.2, self.available_width * 0.6]
        food_table = Table(data, colWidths=col_widths)
        food_table.setStyle(_create_table_style())
        return food_table

    def generate_health_plan(self, patient_info: dict, nutrition_plan: dict, exercise_plan: dict, screening_plan: dict):
        """生成健康管理方案PDF"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建输出目录：{output_dir}")

            doc = SimpleDocTemplate(
                self.output_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            # 使用 List[Any] 类型来支持多种元素类型
            story: List[Any] = [Paragraph("个性化健康管理方案", self.styles['ChineseTitle']), Spacer(1, 20)]
            
            # 添加患者基本信息
            story.extend(self._create_section_header("一、患者基本信息"))
            patient_data = [
                ["性别", "男" if patient_info.get("gender") == "M" else "女"],
                ["年龄", f"{patient_info.get('age', '未知')}岁"],
                ["身高", f"{patient_info.get('height', '未知')}米"],
                ["体重", f"{patient_info.get('weight', '未知')}公斤"],
                ["疾病标签", ", ".join(patient_info.get("tags", []))]
            ]
            # 计算列宽，使表格填满页面
            col_widths = [self.available_width * 0.3, self.available_width * 0.7]  # 30%和70%的宽度分配
            patient_table = Table(patient_data, colWidths=col_widths)
            patient_table.setStyle(_create_table_style())
            story.append(patient_table)
            story.append(Spacer(1, 15))

            # 添加营养方案
            story.extend(self._create_section_header("二、营养方案"))
            story.append(Paragraph("（一）膳食原则及目标", self.styles['ChineseBold']))
            story.extend(self._add_paragraphs_from_text(nutrition_plan.get("overview", "")))
            story.append(Spacer(1, 10))

            story.append(Paragraph("（二）一日膳食能量需要量", self.styles['ChineseBold']))
            intake = nutrition_plan.get("intake", "")
            if intake:
                story.append(Paragraph(f"您的每日膳食能量摄入范围为{intake}千卡", self.styles['Chinese']))
            story.append(Spacer(1, 10))

            # 添加营养素摄入建议
            story.append(Paragraph("（三）一日三餐营养素建议", self.styles['ChineseBold']))
            meal_data = [["营养素", "推荐量（g）", "占总能量比（%）"]]
            for meal in nutrition_plan.get("meal", []):
                meal_data.append([
                    meal.get("name", ""),
                    meal.get("amount", ""),
                    meal.get("ratio", "")
                ])
            # 计算列宽，使表格填满页面
            col_widths = [self.available_width * 0.4, self.available_width * 0.3, self.available_width * 0.3]
            meal_table = Table(meal_data, colWidths=col_widths)
            meal_table.setStyle(_create_table_style())
            story.append(meal_table)
            story.append(Spacer(1, 15))

            # 每日非功能营养素摄入建议
            story.append(Paragraph("（四）每日非功能营养素摄入建议", self.styles['ChineseBold']))
            story.append(self._mock())
            story.append(Spacer(1, 10))

            # 添加膳食结构建议
            story.append(Paragraph("（五）一日三餐膳食结构建议", self.styles['ChineseBold']))
            for structure in nutrition_plan.get("structure", []):
                story.append(Paragraph(f"{structure.get('name', '')}：", self.styles['ChineseBold']))
                items_data = [["食物种类", "推荐量", "食物来源"]]
                for item in structure.get("items", []):
                    items_data.append([
                        item.get("type", ""),
                        f"{item.get('amount', '')}g",
                        item.get("source", "")
                    ])
                # 计算列宽，使表格填满页面
                col_widths = [self.available_width * 0.4, self.available_width * 0.3, self.available_width * 0.3]
                items_table = Table(items_data, colWidths=col_widths)
                items_table.setStyle(_create_table_style())
                story.append(items_table)
                story.append(Spacer(1, 10))

            # 添加营养方案注意事项
            if nutrition_plan.get("note"):
                story.append(Paragraph("（六）其它注意事项", self.styles['ChineseBold']))
                for i, item in enumerate(nutrition_plan.get("note", []), 1):
                    story.append(Paragraph(f"{i}. {item}", self.styles["ChineseList"]))
                story.append(Spacer(1, 15))

            # 添加运动方案
            story.extend(self._create_section_header("三、运动方案"))
            story.append(Paragraph(exercise_plan.get("name", ""), self.styles['Chinese']))
            story.append(Spacer(1, 10))

            # 添加运动处方整体描述
            story.extend(self._add_paragraphs_from_text(exercise_plan.get("overview", "")))
            story.append(Spacer(1, 10))

            # 添加运动目标
            if exercise_plan.get("goal"):
                story.append(Paragraph("（一）运动原则及目标", self.styles['ChineseBold']))
                story.extend(self._add_paragraphs_from_text(exercise_plan.get("goal", "")))
                story.append(Spacer(1, 10))

            # 添加运动建议
            story.append(Paragraph("（二）运动建议：", self.styles['ChineseBold']))
            for component in exercise_plan.get("components", []):
                # 运动类型加粗显示
                story.append(Paragraph(f"<b>{component.get('type', '')}</b>", self.styles['ChineseBold']))
                
                # 其他信息以序号形式展示
                details = [
                    f"1. 运动强度：{component.get('intensity', '')}",
                    f"2. 运动频率：{component.get('frequency', '')}",
                    f"3. 运动时间：{component.get('duration', '')}",
                    f"4. 运动方式：{component.get('recommend', '')}",
                    f"5. 运动方式指导：{component.get('guidance', '')}",
                ]
                
                # 如果有注意事项，添加为第4点
                if component.get("notes"):
                    details.append(f"6. 注意事项：{component.get('notes', '')}")
                
                # 添加所有详细信息
                for detail in details:
                    story.append(Paragraph(detail, self.styles['ChineseList']))
                
                story.append(Spacer(1, 10))

            # 添加需要避免的运动
            if exercise_plan.get("avoid"):
                story.append(Paragraph("（三）需要避免的运动", self.styles['ChineseBold']))
                for i, avoid_item in enumerate(exercise_plan.get("avoid", []), 1):
                    story.append(Paragraph(f"{i}. {avoid_item}", self.styles['ChineseList']))
                story.append(Spacer(1, 10))

            # 添加总体注意事项
            if exercise_plan.get("overall"):
                story.append(Paragraph("（四）总体注意事项", self.styles['ChineseBold']))
                for i, item in enumerate(exercise_plan.get("overall", []), 1):
                    story.append(Paragraph(f"{i}. {item}", self.styles["ChineseList"]))
                story.append(Spacer(1, 10))

            # 添加筛查方案
            story.extend(self._create_section_header("四、筛查方案"))

            # 将概述文本按句号分割，每句单独一行
            story.extend(self._add_paragraphs_from_text(screening_plan.get("overall", "")))
            story.append(Spacer(1, 10))

            story.append(Paragraph("（一）定期随访项目", self.styles['ChineseBold']))
            screening_data = [["项目名称", "随访周期", "管理目标"]]
            for item in screening_plan.get("regular_items", []):

                screening_data.append([
                    item.get("name", ""),
                    item.get("period", ""),
                    item.get("goal", "")
                ])
            # 调整列宽比例，给较长的文本列更多空间
            col_widths = [
                self.available_width * 0.5,  # 项目名称
                self.available_width * 0.2,  # 随访周期
                self.available_width * 0.3,  # 管理目标
            ]
            screening_table = Table(screening_data, colWidths=col_widths, repeatRows=1)
            screening_table.setStyle(_create_screening_table_style())
            story.append(screening_table)
            story.extend(self._add_paragraphs_from_text(screening_plan.get("regular_note", "")))
            story.append(Spacer(1, 15))

            story.append(Paragraph("（二）不适随诊症状", self.styles['ChineseBold']))
            story.append(Paragraph(screening_plan.get("as_needed", ""), self.styles['Chinese']))
            story.append(Spacer(1, 10))

            story.append(Paragraph("（三）温馨提醒", self.styles['ChineseBold']))
            story.extend(self._add_paragraphs_from_text(exercise_plan.get("goal", "")))
            story.append(Paragraph(screening_plan.get("note", ""), self.styles['Chinese']))
            # story.append(Spacer(1, 10))

            # 生成PDF
            print(f"正在生成PDF文件：{self.output_path}")
            doc.build(story)
            print(f"PDF文件生成成功：{self.output_path}")
            
            # 验证文件是否生成
            if os.path.exists(self.output_path):
                print(f"文件大小：{os.path.getsize(self.output_path)} 字节")
            else:
                print("错误：文件未生成")
                
        except Exception as error:
            print(f"生成PDF时发生错误：{str(error)}")
            raise
