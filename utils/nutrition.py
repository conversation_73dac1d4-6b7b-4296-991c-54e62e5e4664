import os
import pandas as pd

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
df = pd.read_excel(os.path.join(project_root, "data", "Nutrient.xlsx"))

def calc_energy(gender: str, age: int, weight: float, height: float, pla: float):
    """ 计算一日膳食能量需要量 (kcal) """
    gender_code = 0 if gender == "M" else 1
    print(gender, age, weight, height, pla)
    if age < 3:
        if gender == "M":
            eer = (0.118 * weight + 3.59 * height - 1.55) * 238.85 * pla * 1.01
        else:
            eer = (0.127 * weight + 2.94 * height - 1.2) * 238.85 * pla * 1.01
    elif 3 <= age < 10:
        if gender == "M":
            eer = (0.0632 * weight + 1.31 * height + 1.28) * 238.85 * pla * 1.01
        else:
            eer = (0.0666 * weight + 0.878 * height + 1.46) * 238.85 * pla * 1.01
    elif 10 <= age < 18:
        if gender == "M":
            eer = (0.0651 * weight + 1.11 * height + 1.25) * 238.85 * pla * 1.01
        else:
            eer = (0.0393 * weight + 1.04 * height + 1.93) * 238.85 * pla * 1.01
    elif 18 <= age < 50:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla
    elif 50 <= age < 65:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.95
    elif 65 <= age < 75:
        print("Asdasdasd: ", gender_code, pla, (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.925)
        print(14.52 * weight - 155.88 * gender_code + 565.79)
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.925
    else:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.9
    return round(eer)

def get_nutrients(age: int, gender: str):

    # 筛选符合年龄要求的记录
    age_mask = (df['age_min'] <= age) & (df['age_max'] >= age)

    # 筛选符合性别要求的记录（性别匹配或未指定）
    gender_mask = (df['gender'].isna()) | (df['gender'] == gender)
    print(gender_mask)
    filtered = df[gender_mask & age_mask].copy()
    print(filtered)

def get_core_nutrients(age: int, eer: int) -> list:
    # 筛选核心营养素
    core_mask = (df['is_core'] == 1.0)
    age_mask = (df['age_min'] <= age) & (df['age_max'] >= age)
    filtered = df[core_mask & age_mask].copy()

    data = []
    for idx, item in filtered.iterrows():
        data.append({
            "name": item["name"],
            "amount": f"{round(eer * item['min_energy_ratio'] / item['kcal_per_gram'], 2)}-{round(eer * item['max_energy_ratio'] / item['kcal_per_gram'], 2)}",
            "ratio": f"{round(item['min_energy_ratio'] * 100, 2)}%-{round(item['max_energy_ratio'] * 100, 2)}%"
        })
    return data

def calc_all(gender: str, age: int, weight: float, height: float, pla: float):
    eer = calc_energy(gender, age, weight, height, pla)
    nutrients = get_core_nutrients(age, eer)
    return {
        "eer": eer,
        "nutrients": nutrients
    }

if __name__ == "__main__":
    result = calc_all("M", 68, 1.71, 81, 1.4)
    print(result)