import os
import pandas as pd

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
df = pd.read_excel(os.path.join(project_root, "data", "Nutrient.xlsx"))


def get_nutrients(age: int, gender: str):

    # 筛选符合年龄要求的记录
    age_mask = (df['age_min'] <= age) & (df['age_max'] >= age)

    # 筛选符合性别要求的记录（性别匹配或未指定）
    gender_mask = (df['gender'].isna()) | (df['gender'] == gender)
    print(gender_mask)
    filtered = df[gender_mask & age_mask].copy()
    print(filtered)

def get_core_nutrients(age: int, gender: str):
    age_mask = (df['age_min'] <= age) & (df['age_max'] >= age)
    filtered = df[age_mask].copy()


if __name__ == "__main__":
    get_nutrients(68, "M")