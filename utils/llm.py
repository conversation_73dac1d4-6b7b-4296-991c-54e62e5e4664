import os

from pydantic import SecretStr
from langchain_openai import Chat<PERSON>penA<PERSON>

def init_langsmith(project_name: str):
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
    os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
    os.environ["LANGCHAIN_PROJECT"] = project_name

local = ChatOpenAI(
    model="gpt-4o",
    base_url="http://192.168.1.12:11435/v1/",
    api_key=SecretStr("123"),
    temperature=0.6
)

qwen3_30b_a3b = ChatOpenAI(
    model="qwen3-30b-a3b",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    api_key=SecretStr("sk-e050b9d5559845099260622cfda9b561"),
    streaming=True
)

def get_llm():
    return local