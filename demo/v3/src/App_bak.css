#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  min-height: 100vh;
  height: auto;
  background-color: #f50000;
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.ant-form-item-explain {
  text-align: left;
}

/* 移除按钮默认的黑框焦点样式 */
.ant-btn:focus,
.ant-btn:active {
  outline: none !important;
  box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1) !important;
}

/* Antd 官网样式的按钮焦点效果 */
.ant-btn-primary:focus,
.ant-btn-primary:active {
  outline: none !important;
  box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1) !important;
  border-color: #40a9ff !important;
}

/* 按钮悬停效果 */
.ant-btn-primary:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 移除所有元素的默认焦点样式 */
*:focus {
  outline: none !important;
}

/* 为可访问性保留键盘导航的焦点样式 */
.ant-btn:focus-visible {
  outline: 2px solid #1890ff !important;
  outline-offset: 2px !important;
}

/* PC端布局优化 */
.layout {
  min-height: 100vh !important;
}

.main-container {
  min-height: calc(100vh - 80px) !important;
  padding-bottom: 40px !important;
}

.content-area .ant-card {
  margin-bottom: 40px !important;
}

.report-content {
  padding-bottom: 40px !important;
}

/* 桌面端（默认）布局 */
.app-layout-container {
  display: flex;
  flex-direction: row;
  gap: 32px;
  width: 100%;
  align-items: flex-start;
}
.patient-form-card {
  min-width: 400px;
  max-width: 500px;
  width: 100%;
}
.plan-display-area {
  flex: 1;
  min-width: 400px;
}
.ant-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06) !important;
  border: none !important;
  background: #fff !important;
  margin: 0 !important;
}

/* 移动端布局 */
@media (max-width: 768px) {
  .app-layout-container {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    align-items: stretch !important;
    gap: 0 !important;
    min-width: 0 !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  .ant-card {
    margin: 8px !important;
    border-radius: 12px !important;
    width: calc(100% - 16px) !important;
    box-sizing: border-box !important;
    background: #fff !important;
  }
  .ant-card-head {
    background: #fff !important;
  }
  .ant-card-body {
    padding-left: 12px !important;
    padding-right: 12px !important;
    background: #fff !important;
  }
  .ant-card:last-child {
    margin-bottom: 8px !important;
  }
  .app-content,
  .plan-display-area,
  .patient-form-card {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    background: transparent !important;
  }
  body, html, #root {
    background: #f7f8fa !important;
  }
  .plan-display-area {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    flex: none !important;
  }
}

/* 主布局调整 */
.main-container {
  flex-direction: column !important;
  gap: 0 !important;
  padding: 12px !important;
  padding-bottom: 12px !important;
  min-height: auto !important;
}

/* 重置PC端的底部间距 */
.content-area .ant-card {
  margin-bottom: 8px !important;
}

.report-content {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

/* 左侧表单卡片 */
.form-card {
  flex: none !important;
  width: 100% !important;
  min-width: auto !important;
}

/* 右侧内容区域 */
.content-area {
  flex: none !important;
  width: 100% !important;
  min-width: auto !important;
}

/* 表单内边距调整 */
.form-container {
  padding: 0 8px !important;
}

/* 报告内容内边距调整 */
.report-content {
  padding: 0 8px !important;
}

/* 表格在手机端的适配 */
.ant-table {
  font-size: 12px !important;
  overflow-x: auto !important;
}

.ant-table-thead > tr > th {
  padding: 8px 4px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
}

.ant-table-tbody > tr > td {
  padding: 8px 4px !important;
  font-size: 12px !important;
  word-break: break-word !important;
}

/* 表格容器滚动 */
.ant-table-container {
  overflow-x: auto !important;
}

/* 卡片在手机端的适配 */
.ant-card {
  margin-bottom: 8px !important;
  margin-left: 8px !important;
  margin-right: 8px !important;
}

.ant-card-body {
  padding: 12px !important;
}

/* 标题字体调整 */
h4 {
  font-size: 16px !important;
  margin-bottom: 8px !important;
}

h5 {
  font-size: 14px !important;
  margin-bottom: 6px !important;
}

/* 段落字体调整 */
p {
  font-size: 13px !important;
  line-height: 1.4 !important;
  margin-bottom: 6px !important;
}

/* 列表字体调整 */
li {
  font-size: 13px !important;
  line-height: 1.4 !important;
  margin-bottom: 2px !important;
}

/* 运动组件卡片调整 */
.exercise-component {
  padding: 4px !important;
  margin-bottom: 6px !important;
}

.exercise-component p {
  margin: 0 0 1px 0 !important;
  font-size: 12px !important;
}

/* 按钮调整 */
.ant-btn-lg {
  height: 48px !important;
  font-size: 16px !important;
}

/* 输入框调整 */
.ant-input-number {
  font-size: 16px !important;
}

/* Radio和Checkbox调整 */
.ant-radio-wrapper,
.ant-checkbox-wrapper {
  font-size: 14px !important;
  margin-bottom: 8px !important;
}

/* 卡片标题调整 */
.ant-card-head-title {
  font-size: 16px !important;
}

/* 表单标签调整 */
.ant-form-item-label > label {
  font-size: 14px !important;
}

/* 超小屏幕适配 (iPhone SE等) */
@media (max-width: 375px) {
  .main-container {
    padding: 8px !important;
    gap: 0 !important;
  }

  .form-container,
  .report-content {
    padding: 0 4px !important;
  }

  .ant-table {
    font-size: 11px !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 6px 2px !important;
    font-size: 11px !important;
  }

  h4 {
    font-size: 15px !important;
  }

  h5 {
    font-size: 13px !important;
  }

  p, li {
    font-size: 12px !important;
  }
}

/* 仅桌面端悬浮，移动端不悬浮 */
.sticky-on-desktop {
  position: sticky;
  top: 20px;
  z-index: 10;
}

@media (max-width: 767px) {
  .sticky-on-desktop {
    position: static !important;
    top: auto !important;
  }
}
