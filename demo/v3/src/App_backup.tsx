import {useState} from 'react'
import {<PERSON><PERSON>, Card, Checkbox, Divider, Form, InputNumber, Layout, message, Radio, Table, Typography } from 'antd'
import './App.css'

const { Content } = Layout
const { Title, Paragraph } = Typography

interface PatientFormData {
  gender: 'M' | 'F'
  age: number
  height: number
  weight: number
  activityLevel: number
  hyperlipidemia: string // 高脂血症单选
  diabetes: boolean // 糖尿病复选框
}

interface ReportData {
  patient: {
    gender: string
    age: number
    height: number
    weight: number
    pla: number
    tags: string[]
  }
  nutrition_metrics: {
    eer: number
    core: Array<{
      name: string
      unit: string
      amount: string
      ratio: string
    }>
    other: Array<{
      name: string
      unit: string
      amount: string
      food: string
    }>
  }
  nutrition: {
    overview: string
    structure: Array<{
      name: string
      items: Array<{
        type: string
        amount: number
        source: string
      }>
    }>
    note: string[]
  }
  exercise: {
    name: string
    overview: string
    goal: string
    components: Array<{
      type: string
      intensity: string
      frequency: string
      duration: string
      recommend: string
      guidance: string
      notes: string
    }>
    avoid: string[]
    overall: string[]
  }
  screening: {
    overall: string
    regular_items: Array<{
      name: string
      period: string
      goal: string | null
    }>
    regular_note: string
    as_needed: string
    note: string
  }
}

function App() {
  const [form] = Form.useForm<PatientFormData>()
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(false)

  const generateReport = async (formData: PatientFormData): Promise<ReportData> => {
    // 构建请求数据
    const requestData = {
      gender: formData.gender,
      age: formData.age,
      height: formData.height / 100, // 转换为米
      weight: formData.weight,
      pla: formData.activityLevel,
      tags: [
        ...(formData.hyperlipidemia ? [`高脂血症${formData.hyperlipidemia}`] : []),
        ...(formData.diabetes ? ['糖尿病'] : [])
      ]
    }

    console.log('发送请求数据:', requestData)

    try {
      // 调用后端接口
      const response = await fetch('http://127.0.0.1:8000/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('接口调用失败:', error)
      throw error
    }
  }

  const onFinish = async (values: PatientFormData) => {
    console.log('表单数据:', values)
    
    // 开始生成新报告时，先清空之前的报告数据
    setReportData(null)
    setLoading(true)

    try {
      const report = await generateReport(values)
      setReportData(report)
      message.success('体检报告生成成功！')
    } catch (error) {
      message.error('生成报告失败，请稍后重试')
      console.error('生成报告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout className="layout" style={{ width: '100%', minHeight: '100vh' }}>
      <Content style={{ padding: '40px', width: '100%' }}>
        <div className="main-container" style={{ display: 'flex', gap: '40px', width: '100%' }}>
          {/* 左侧表单 */}
          <Card
            title="患者信息"
            className="form-card"
            style={{
              flex: '0 0 500px',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              initialValues={{
                gender: 'M',
                activityLevel: 1.0,
                hyperlipidemia: '',
                diabetes: false
              }}
              className="form-container"
              style={{ padding: '0 20px' }}
            >
              <Form.Item
                label="性别"
                name="gender"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="M" style={{ marginBottom: '8px' }}>男</Radio>
                  <Radio value="F" style={{ marginBottom: '8px' }}>女</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="年龄"
                name="age"
                rules={[
                  { required: true, message: '请输入年龄' },
                  { type: 'number', min: 18, max: 150, message: '年龄必须在18-150岁之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={18}
                  max={150}
                />
              </Form.Item>

              <Form.Item
                label="身高（厘米）"
                name="height"
                rules={[
                  { required: true, message: '请输入身高' },
                  { type: 'number', min: 100, max: 250, message: '身高必须在100-250厘米之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={100}
                  max={250}
                />
              </Form.Item>

              <Form.Item
                label="体重（公斤）"
                name="weight"
                rules={[
                  { required: true, message: '请输入体重' },
                  { type: 'number', min: 20, max: 200, message: '体重必须在20-200公斤之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={20}
                  max={200}
                />
              </Form.Item>

              <Form.Item
                label="身体活动水平"
                name="activityLevel"
                rules={[
                  { required: true, message: '请输入身体活动水平' },
                  { type: 'number', min: 1, max: 3, message: '身体活动水平必须在1-3之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                  min={1}
                  max={3}
                />
              </Form.Item>

              <Divider orientation="left">疾病信息</Divider>

              <Form.Item
                label="高脂血症风险等级"
                name="hyperlipidemia"
                rules={[{ required: true, message: '请选择高脂血症风险等级' }]}
                tooltip="只能选择一个风险等级"
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="低危" style={{ marginBottom: '8px' }}>低危</Radio>
                  <Radio value="中危" style={{ marginBottom: '8px' }}>中危</Radio>
                  <Radio value="高危" style={{ marginBottom: '8px' }}>高危</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="合并症"
                name="diabetes"
                valuePropName="checked"
                tooltip="可以与高脂血症同时选择"
              >
                <div style={{ textAlign: 'left' }}>
                  <Checkbox>糖尿病</Checkbox>
                </div>
              </Form.Item>

              <Form.Item style={{ marginTop: '24px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loading}
                >
                  {loading ? '生成报告中...' : '生成体检报告'}
                </Button>
              </Form.Item>
            </Form>
          </Card>

          {/* 右侧内容区域 */}
          <div className="content-area" style={{ flex: 1, minWidth: '500px' }}>
            {reportData && (
              <Card
                title="个性化健康管理报告"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                  maxHeight: '90vh',
                  overflow: 'auto'
                }}
              >
                <div className="report-content" style={{ padding: '0 20px', textAlign: 'left' }}>
                  {/* 患者基本信息 */}
                  <h4 style={{ textAlign: 'left', color: '#1890ff' }}>患者基本信息</h4>
                  <p style={{ textAlign: 'left' }}><strong>性别:</strong> {reportData.patient.gender === 'M' ? '男' : '女'}</p>
                  <p style={{ textAlign: 'left' }}><strong>年龄:</strong> {reportData.patient.age} 岁</p>
                  <p style={{ textAlign: 'left' }}><strong>身高:</strong> {reportData.patient.height} 米</p>
                  <p style={{ textAlign: 'left' }}><strong>体重:</strong> {reportData.patient.weight} 公斤</p>
                  <p style={{ textAlign: 'left' }}><strong>身体活动水平:</strong> {reportData.patient.pla}</p>
                  <p style={{ textAlign: 'left' }}><strong>疾病标签:</strong> {reportData.patient.tags.join('、')}</p>

                  <Divider />

                  {/* 营养指标 */}
                  <h4 style={{ textAlign: 'left', color: '#1890ff' }}>营养指标</h4>
                  <p style={{ textAlign: 'left' }}><strong>每日能量需求:</strong> {reportData.nutrition_metrics.eer} kcal</p>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>核心营养素</h5>
                  <Table
                    dataSource={reportData.nutrition_metrics.core.map((item, index) => ({
                      key: index,
                      nutrient: item.name,
                      amount: item.amount + item.unit,
                      ratio: item.ratio
                    }))}
                    columns={[
                      {
                        title: '营养素',
                        dataIndex: 'nutrient',
                        key: 'nutrient',
                        width: 100
                      },
                      {
                        title: '推荐量（g）',
                        dataIndex: 'amount',
                        key: 'amount',
                        width: 120
                      },
                      {
                        title: '占总能量比（%）',
                        dataIndex: 'ratio',
                        key: 'ratio',
                        width: 130
                      }
                    ]}
                    pagination={false}
                    size="small"
                    scroll={{ x: 350 }}
                    style={{ marginBottom: '8px' }}
                  />

                  <Divider />

                  {/* 营养方案 */}
                  <h4 style={{ textAlign: 'left', color: '#1890ff' }}>营养方案</h4>
                  <div style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '16px' }}>
                    {reportData.nutrition.overview.split('\n\n').map((paragraph, index) => (
                      <p key={index} style={{ marginBottom: '12px' }}>{paragraph}</p>
                    ))}
                  </div>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>膳食结构建议</h5>
                  {reportData.nutrition.structure.map((meal, index) => (
                    <div key={index} style={{ marginBottom: '12px' }}>
                      <strong>{meal.name}:</strong>
                      <ul style={{ paddingLeft: '20px', marginTop: '4px' }}>
                        {meal.items.map((item, itemIndex) => (
                          <li key={itemIndex}>{item.type}: {item.amount}g ({item.source})</li>
                        ))}
                      </ul>
                    </div>
                  ))}

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>注意事项</h5>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    {reportData.nutrition.note.map((note, index) => (
                      <li key={index} style={{ marginBottom: '4px' }}>{note}</li>
                    ))}
                  </ul>

                  <Divider />

                  {/* 运动方案 */}
                  <h4 style={{ textAlign: 'left', color: '#1890ff' }}>运动方案</h4>
                  <h5 style={{ textAlign: 'left' }}>{reportData.exercise.name}</h5>
                  <p style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '12px' }}>
                    {reportData.exercise.overview}
                  </p>
                  <p style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '16px' }}>
                    <strong>目标:</strong> {reportData.exercise.goal}
                  </p>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>运动组成</h5>
                  {reportData.exercise.components.map((component, index) => (
                    <div key={index} className="exercise-component" style={{ marginBottom: '4px', padding: '4px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                      <p style={{ margin: '0 0 4px 0' }}><strong>{component.type}</strong> - {component.intensity}强度</p>
                      <p style={{ margin: '0 0 4px 0' }}><strong>频率:</strong> {component.frequency}</p>
                      <p style={{ margin: '0 0 4px 0' }}><strong>时长:</strong> {component.duration}</p>
                      <p style={{ margin: '0 0 4px 0' }}><strong>推荐:</strong> {component.recommend}</p>
                      <p style={{ margin: '0 0 4px 0' }}><strong>指导:</strong> {component.guidance}</p>
                      <p style={{ margin: '0' }}><strong>注意:</strong> {component.notes}</p>
                    </div>
                  ))}

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>避免运动</h5>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    {reportData.exercise.avoid.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>总体建议</h5>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    {reportData.exercise.overall.map((item, index) => (
                      <li key={index} style={{ marginBottom: '4px' }}>{item}</li>
                    ))}
                  </ul>

                  <Divider />

                  {/* 筛查方案 */}
                  <h4 style={{ textAlign: 'left', color: '#1890ff' }}>筛查方案</h4>
                  <p style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '16px' }}>
                    {reportData.screening.overall}
                  </p>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>定期检查项目</h5>
                  {reportData.screening.regular_items.map((item, index) => (
                    <p key={index} style={{ textAlign: 'left', marginBottom: '4px' }}>
                      <strong>{item.name}:</strong> {item.period}
                      {item.goal && ` (目标: ${item.goal})`}
                    </p>
                  ))}

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>检查注意事项</h5>
                  <p style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '12px' }}>
                    {reportData.screening.regular_note}
                  </p>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>紧急就医指征</h5>
                  <p style={{ textAlign: 'left', lineHeight: '1.6', marginBottom: '12px' }}>
                    {reportData.screening.as_needed}
                  </p>

                  <h5 style={{ textAlign: 'left', marginTop: '16px' }}>总结</h5>
                  <p style={{ textAlign: 'left', lineHeight: '1.6' }}>
                    {reportData.screening.note}
                  </p>
                </div>
              </Card>
            )}

            {!reportData && !loading && (
              <Card
                title="使用说明"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{ padding: '0 20px', textAlign: 'left' }}>
                  <h4 style={{ textAlign: 'left' }}>填写表单生成个性化健康管理报告</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>填写完整的患者基本信息</li>
                    <li>选择高脂血症风险等级（低危/中危/高危）</li>
                    <li>选择是否患有糖尿病</li>
                    <li>点击"生成体检报告"按钮</li>
                  </ul>

                  <h4 style={{ textAlign: 'left' }}>报告内容包括</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li><strong>营养方案</strong>：个性化膳食建议和营养指标</li>
                    <li><strong>运动方案</strong>：针对性的运动处方和注意事项</li>
                    <li><strong>筛查方案</strong>：定期检查项目和健康监测</li>
                  </ul>
                </div>
              </Card>
            )}

            {loading && (
              <Card
                title="正在生成报告..."
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ padding: '40px' }}>
                  <p>正在根据您的信息生成个性化健康管理报告，请稍候...</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </Content>
    </Layout>
  )
}

export default App
