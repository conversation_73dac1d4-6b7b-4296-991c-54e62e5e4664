import ReactDOM from "react-dom/client"
import {createBrowserRouter, redirect, RouterProvider} from "react-router";


import './index.css'
import Login from './Login.tsx'
import App from './App.tsx'

// const root = document.getElementById("root")!
//
// ReactDOM.createRoot(root).render(
//   <BrowserRouter>
//     <Routes>
//       <Route path="/login" element={<Login />} />
//       <Route path="/" element={<App />} />
//     </Routes>
//   </BrowserRouter>
// )

const router = createBrowserRouter([
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/",
    element: <App />,
    loader: protectedLoader
  }
])

ReactDOM.createRoot(document.getElementById('root')!).render(
    <RouterProvider router={router} />
)

export async function protectedLoader() {
  const token = localStorage.getItem('token')
  if (!token) {
    throw redirect("/login");
  }

  return null;
}