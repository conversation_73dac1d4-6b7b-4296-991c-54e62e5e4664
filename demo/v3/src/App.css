#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  min-height: 100vh;
  height: auto;
  background-color: #f7f8fa;
}

/* 移除所有元素的默认焦点样式 */
*:focus {
  outline: none !important;
}

.ant-card {
  margin: 8px !important;
  border-radius: 12px !important;
  width: calc(100% - 16px) !important;
  box-sizing: border-box !important;
  background: #fff !important;
}

/* 移动端布局 */
@media (max-width: 768px) {
  .app-layout-container {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    align-items: stretch !important;
    gap: 0 !important;
    min-width: 0 !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
}

/* 仅桌面端悬浮，移动端不悬浮 */
.sticky-on-desktop {
  position: sticky;
  z-index: 10;
}

@media (max-width: 768px) {
  .sticky-on-desktop {
    position: static !important;
    top: auto !important;
  }
}