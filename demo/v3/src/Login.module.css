/* 登录页面 CSS Modules 样式 */

/* 登录容器 */
.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 登录卡片 */
.loginCard {
  animation: fadeInUp 0.6s ease-out;
}

/* 登录表单 */
.loginForm {
  /* 表单特定样式 */
}

/* 登录输入框 */
.loginInput {
  border-radius: 12px !important;
  border: 2px solid #e8f4fd !important;
  box-shadow: none !important;
}

.loginInput:focus,
.loginInput:hover {
  border-color: #2980f2 !important;
  box-shadow: 0 0 0 2px rgba(41, 128, 242, 0.1) !important;
  outline: none !important;
}

/* 登录按钮 */
.loginButton {
  background: linear-gradient(90deg, #2980f2 0%, #6dd5fa 100%) !important;
  border: none !important;
  font-weight: 600 !important;
  letter-spacing: 1px !important;
  border-radius: 12px !important;
  height: 48px !important;
  font-size: 16px !important;
}

.loginButton:hover {
  background: linear-gradient(90deg, #1e6bb8 0%, #5bc0de 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(41, 128, 242, 0.3) !important;
  transition: all 0.3s ease;
}

.loginButton:active {
  transform: translateY(0px);
  box-shadow: 0 2px 8px rgba(41, 128, 242, 0.2) !important;
}

/* Logo 容器 */
.logoContainer {
  display: inline-block;
  padding: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2980f2 0%, #6dd5fa 100%);
  margin-bottom: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.logoIcon {
  font-size: 32px;
  color: white;
  display: block;
}

/* 标题样式 */
.loginTitle {
  margin: 0 !important;
  color: #2980f2 !important;
  font-weight: 700 !important;
  letter-spacing: 2px !important;
  background: linear-gradient(135deg, #2980f2 0%, #6dd5fa 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.loginSubtitle {
  color: #64748b !important;
  font-size: 14px !important;
  display: block !important;
  margin-top: 8px !important;
}

/* 输入框图标 */
.inputIcon {
  color: #2980f2 !important;
  transition: all 0.3s ease;
}

/* 表单项样式 */
.formItem {
  /* 表单项特定样式 */
}

/* 错误信息样式 */
.errorMessage {
  font-size: 12px !important;
  margin-top: 4px !important;
  text-align: left !important;
}

/* 动画定义 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 128, 242, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(41, 128, 242, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 128, 242, 0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 背景动画效果 */
.loginContainer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, 
    rgba(255,255,255,0.1) 0%, 
    transparent 50%, 
    rgba(255,255,255,0.1) 100%);
  animation: shimmer 3s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loginCard {
    width: 90vw !important;
    min-width: 280px !important;
    max-width: 350px !important;
  }
}

@media (max-width: 480px) {
  .loginCard {
    width: 95vw !important;
    min-width: 260px !important;
    max-width: 320px !important;
  }
  
  .loginTitle {
    font-size: 16px !important;
  }
}

/* 毛玻璃效果 */
.loginCard {
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}
