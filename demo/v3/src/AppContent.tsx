import React, { useState } from 'react';
import {
  Form,
  Radio,
  InputNumber,
  Select,
  Card,
  Button,
  Typography,
  Divider,
  message,
  Table,
  Row,
  Col
} from 'antd';
import './App.css';

const { Title, Paragraph } = Typography;

interface PatientFormData {
  gender: 'M' | 'F';
  age: number;
  height: number;
  weight: number;
  activityLevel: number;
  diseaseTags: string[];
}

interface ReportData {
  patient: {
    gender: string;
    age: number;
    height: number;
    weight: number;
    pla: number;
    tags: string[];
  };
  nutrition_metrics: {
    eer: number;
    core: Array<{
      name: string;
      unit: string;
      amount: string;
      ratio: string;
    }>;
    other: Array<{
      name: string;
      unit: string;
      amount: string;
      food: string;
    }>;
  };
  nutrition: {
    overview: string;
    structure: Array<{
      name: string;
      items: Array<{
        type: string;
        amount: number;
        source: string;
      }>;
    }>;
    note: string[];
  };
  exercise: {
    name: string;
    overview: string;
    goal: string;
    components: Array<{
      type: string;
      intensity: string;
      frequency: string;
      duration: string;
      recommend: string;
      guidance: string;
      notes: string;
    }>;
    avoid: string[];
    overall: string[];
  };
  screening: {
    overall: string;
    regular_items: Array<{
      name: string;
      period: string;
      goal: string | null;
    }>;
    regular_note: string;
    as_needed: string;
    note: string;
  };
}

const AppContent: React.FC = () => {
  const [form] = Form.useForm<PatientFormData>();
  const [selectedDiseaseTags, setSelectedDiseaseTags] = useState<string[]>([]);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);

  const handleDiseaseTagsChange = (value: string[]) => {
    const highRiskTags = ['高脂血症低危', '高脂血症中危', '高脂血症高危'];
    const diabetesTag = '糖尿病';
    const newHighRiskSelections = value.filter(tag => highRiskTags.includes(tag));
    const hasDiabetes = value.includes(diabetesTag);
    const nextSelectedTags: string[] = [];
    if (newHighRiskSelections.length > 0) {
      const lastSelectedHighRisk = newHighRiskSelections[newHighRiskSelections.length - 1];
      nextSelectedTags.push(lastSelectedHighRisk);
    }
    if (hasDiabetes) {
      nextSelectedTags.push(diabetesTag);
    }
    setSelectedDiseaseTags(nextSelectedTags);
  };

  const generateReport = async (formData: PatientFormData): Promise<ReportData> => {
    const requestData = {
      gender: formData.gender,
      age: formData.age,
      height: formData.height,
      weight: formData.weight,
      pla: formData.activityLevel,
      tags: formData.diseaseTags
    };
    try {
      const response = await fetch('http://127.0.0.1:8000/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      const report = await response.json();
      return report;
    } catch (error) {
      console.error('接口调用失败:', error);
      throw error;
    }
  };

  const onFinish = async (values: PatientFormData) => {
    const heightInMeter = values.height / 100;
    const dataToSubmit = { ...values, height: heightInMeter, diseaseTags: selectedDiseaseTags };
    setReportData(null);
    setLoading(true);
    try {
      const report = await generateReport(dataToSubmit);
      setReportData(report);
      message.success('报告生成成功！');
    } catch (error) {
      message.error('生成报告失败，请稍后重试');
      console.error('生成报告失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Row>
      <Col span={24} lg={6}>
        <Card title="患者信息" className='sticky-on-desktop'>
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            initialValues={{
              gender: 'M',
              age: 68,
              height: 178,
              weight: 68,
              activityLevel: 1.6
            }}
            style={{ padding: '0 20px' }}
          >
            {/* ...表单内容同原App.tsx... */}
            {/* 省略，直接复制原App.tsx表单内容 */}
          </Form>
        </Card>
      </Col>
      <Col span={24} lg={18}>
        {/* ...右侧内容同原App.tsx... */}
      </Col>
    </Row>
  );
};

export default AppContent; 