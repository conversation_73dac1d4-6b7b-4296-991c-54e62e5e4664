/* 登录页面样式 */

/* 表单项标签样式 */
.ant-form-item-label > label {
  color: #2c3e50 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

/* 错误信息样式 */
.ant-form-item-explain-error {
  font-size: 12px !important;
  margin-top: 4px !important;
  text-align: left;
}

/* 卡片动画效果 */
.ant-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 128, 242, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(41, 128, 242, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(41, 128, 242, 0);
  }
}

/* 背景动画效果 */
.login-container {
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, 
    rgba(255,255,255,0.1) 0%, 
    transparent 50%, 
    rgba(255,255,255,0.1) 100%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 图标动画 */
.anticon {
  transition: all 0.3s ease;
}

.ant-input:focus .anticon,
.ant-input-password:focus .anticon {
  color: #2980f2 !important;
  transform: scale(1.1);
}

/* 加载状态优化 */
.ant-btn-loading .ant-btn-loading-icon {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-card {
    width: 90vw !important;
    min-width: 280px !important;
    max-width: 350px !important;
  }

  .ant-card .ant-card-body {
    padding: 32px 24px 24px 24px !important;
  }

  .ant-typography h3 {
    font-size: 18px !important;
  }
}

@media (max-width: 480px) {
  .ant-card {
    width: 95vw !important;
    min-width: 260px !important;
    max-width: 320px !important;
  }

  .ant-card .ant-card-body {
    padding: 24px 20px 20px 20px !important;
  }

  .ant-typography h3 {
    font-size: 16px !important;
  }
}

/* 毛玻璃效果增强 */
.ant-card {
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 输入框内图标颜色过渡 */
.ant-input-prefix .anticon {
  transition: color 0.3s ease;
}

/* 分割线样式 */
.ant-divider-vertical {
  border-left-color: rgba(41, 128, 242, 0.2) !important;
}

/* 提示文字样式 */
.ant-typography {
  transition: color 0.3s ease;
}

.ant-typography:hover {
  color: #2980f2 !important;
}
