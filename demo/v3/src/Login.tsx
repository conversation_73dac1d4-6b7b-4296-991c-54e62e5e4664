import React, {useEffect, useState} from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined, SafetyOutlined } from '@ant-design/icons';
import styles from './Login.module.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()

  useEffect(() => {
    localStorage.removeItem("token");
  }, []);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true)
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/login`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(values)
      })
      const result = await response.json();
      if (result.success) {
        messageApi.success('登录成功！');
        // 存储token
        localStorage.setItem('token', result.data.token)
        localStorage.setItem('username', result.data.username)
        // 这里可以跳转到主页面
        window.location.href = '/'
      } else {
        messageApi.error(result.message || '登录失败');
      }
    } catch (e) {
      console.log(e)
      messageApi.error('网络错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.loginContainer}>
      {contextHolder}
      <Card
        className={styles.loginCard}
        style={{
          width: '33.33vw',
          minWidth: 320,
          maxWidth: 400,
          boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)',
          borderRadius: 18,
          background: 'rgba(255,255,255,0.92)',
          border: 'none',
          backdropFilter: 'blur(4px)',
        }}
        styles={{ body: { padding: '40px 36px 32px 36px' } }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <div className={styles.logoContainer}>
            <SafetyOutlined className={styles.logoIcon} />
          </div>
          <Title level={3} className={styles.loginTitle}>
            智能随访系统
          </Title>
          <Text className={styles.loginSubtitle}>
            欢迎回来，请登录您的账户
          </Text>
        </div>
        <Form
          name="login"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
          className={styles.loginForm}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
            className={styles.formItem}
          >
            <Input
              prefix={<UserOutlined className={styles.inputIcon} />}
              size="large"
              placeholder="请输入用户名"
              autoComplete="username"
              className={styles.loginInput}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' }
            ]}
            className={styles.formItem}
          >
            <Input.Password
              prefix={<LockOutlined className={styles.inputIcon} />}
              size="large"
              placeholder="请输入密码"
              autoComplete="current-password"
              className={styles.loginInput}
            />
          </Form.Item>

          <Form.Item style={{ marginTop: 32, marginBottom: 16 }}>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              block
              loading={loading}
              icon={<LoginOutlined />}
              className={styles.loginButton}
            >
              {loading ? '登录中...' : '立即登录'}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 