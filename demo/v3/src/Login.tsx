import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import './App.css';

const { Title } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      // 这里可以替换为实际的登录请求
      // const res = await fetch('/api/login', ...)
      if (values.username === 'admin' && values.password === '123456') {
        message.success('登录成功！');
      } else {
        message.error('用户名或密码错误');
      }
    } catch (e) {
      console.log(e)
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        width: '100vw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #2980f2 0%, #6dd5fa 100%)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <Card
        style={{
          width: 380,
          boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)',
          borderRadius: 18,
          background: 'rgba(255,255,255,0.92)',
          border: 'none',
          backdropFilter: 'blur(4px)',
        }}
        bodyStyle={{ padding: '40px 36px 32px 36px' }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          {/* 可替换为logo图片 */}
          <img src="https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/1f4bb.svg" alt="logo" style={{ width: 48, marginBottom: 12 }} />
          <Title level={3} style={{ margin: 0, color: '#2980f2', fontWeight: 700, letterSpacing: 2 }}>iFollow 登录</Title>
        </div>
        <Form
          name="login"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input size="large" placeholder="请输入用户名" autoComplete="username" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password size="large" placeholder="请输入密码" autoComplete="current-password" />
          </Form.Item>

          <Form.Item style={{ marginTop: 32 }}>
            <Button type="primary" htmlType="submit" size="large" block loading={loading} style={{ background: 'linear-gradient(90deg, #2980f2 0%, #6dd5fa 100%)', border: 'none', fontWeight: 600, letterSpacing: 1 }}>
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 