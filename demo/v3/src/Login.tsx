import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import './App.css';

const { Title } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      // 这里可以替换为实际的登录请求
      // const res = await fetch('/api/login', ...)
      if (values.username === 'admin' && values.password === '123456') {
        message.success('登录成功！');
      } else {
        message.error('用户名或密码错误');
      }
    } catch (e) {
      message.error('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#f7f8fa' }}>
      <Card
        style={{ width: 360, boxShadow: '0 2px 12px rgba(0,0,0,0.08)', borderRadius: 12 }}
        bodyStyle={{ padding: '32px 32px 24px 32px' }}
      >
        <Title level={3} style={{ textAlign: 'center', marginBottom: 32 }}>用户登录</Title>
        <Form
          name="login"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input size="large" placeholder="请输入用户名" autoComplete="username" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password size="large" placeholder="请输入密码" autoComplete="current-password" />
          </Form.Item>

          <Form.Item style={{ marginTop: 32 }}>
            <Button type="primary" htmlType="submit" size="large" block loading={loading}>
              登录
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login; 