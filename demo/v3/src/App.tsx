import { useState } from 'react'
import { Layout, Form, Radio, InputNumber, Select, Card } from 'antd'
import './App.css'

const { Content } = Layout

interface PatientFormData {
  gender: 'M' | 'F'
  age: number
  height: number
  weight: number
  activityLevel: number
  diseaseTags: string[]
}

function App() {
  const [form] = Form.useForm<PatientFormData>()
  const [selectedDiseaseTags, setSelectedDiseaseTags] = useState<string[]>([])

  console.log("Current selectedDiseaseTags state:", selectedDiseaseTags);

  const handleDiseaseTagsChange = (value: string[]) => {
    const highRiskTags = ['高脂血症低危', '高脂血症中危', '高脂血症高危']
    
    // 分离新的选中值中的高脂血症标签和其他标签
    const newHighRiskSelections = value.filter(tag => highRiskTags.includes(tag));
    const newOtherSelections = value.filter(tag => !highRiskTags.includes(tag));

    let nextSelectedTags = newOtherSelections; // 从其他标签开始

    // 如果本次选中包含高脂血症标签，只保留最后一个选中的高脂血症标签
    if (newHighRiskSelections.length > 0) {
      // 找到 value 数组中最后一个出现的高脂血症标签
      for (let i = value.length - 1; i >= 0; i--) {
        if (highRiskTags.includes(value[i])) {
          nextSelectedTags = [...newOtherSelections, value[i]];
          break; // 找到最后一个就停止
        }
      }
    }

    console.log('nextSelectedTags:', nextSelectedTags);
    setSelectedDiseaseTags(nextSelectedTags);
  };

  return (
    <Layout className="layout" style={{ width: '100%', minHeight: '100vh' }}>
      <Content style={{ padding: '40px', width: '100%' }}>
        <div style={{ display: 'flex', gap: '40px', width: '100%' }}>
          {/* 左侧表单 */}
          <Card 
            title="患者信息" 
            style={{ 
              flex: '0 0 500px',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                gender: 'M',
                activityLevel: 1.0
              }}
              style={{ padding: '0 20px' }}
            >
              <Form.Item
                label="性别"
                name="gender"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Radio.Group>
                  <Radio value="M">男</Radio>
                  <Radio value="F">女</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="年龄"
                name="age"
                rules={[
                  { required: true, message: '请输入年龄' },
                  { type: 'number', min: 18, max: 150, message: '年龄必须在18-150岁之间' }
                ]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                label="身高（米）"
                name="height"
                rules={[
                  { required: true, message: '请输入身高' },
                  { type: 'number', min: 1, max: 2.5, message: '身高必须在1-2.5米之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                />
              </Form.Item>

              <Form.Item
                label="体重（公斤）"
                name="weight"
                rules={[
                  { required: true, message: '请输入体重' },
                  { type: 'number', min: 20, max: 200, message: '体重必须在20-200公斤之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                />
              </Form.Item>

              <Form.Item
                label="身体活动水平"
                name="activityLevel"
                rules={[
                  { required: true, message: '请输入身体活动水平' },
                  { type: 'number', min: 0.5, max: 3, message: '身体活动水平必须在0.5-3之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                />
              </Form.Item>

              <Form.Item
                label="疾病标签"
                name="diseaseTags"
                rules={[{ required: true, message: '请选择疾病标签' }]}
              >
                <Select
                  mode="multiple"
                  style={{ width: '100%' }}
                  placeholder="请选择疾病标签"
                  onChange={handleDiseaseTagsChange}
                  value={selectedDiseaseTags}
                  key={JSON.stringify(selectedDiseaseTags)}
                  options={[
                    { label: '高脂血症低危', value: '高脂血症低危' },
                    { label: '高脂血症中危', value: '高脂血症中危' },
                    { label: '高脂血症高危', value: '高脂血症高危' },
                    { label: '糖尿病', value: '糖尿病' }
                  ]}
                />
              </Form.Item>
            </Form>
          </Card>

          {/* 右侧内容区域 */}
          <div style={{ flex: 1, minWidth: '500px' }}>
            {/* 这里可以放置右侧的内容 */}
          </div>
        </div>
      </Content>
    </Layout>
  )
}

export default App
