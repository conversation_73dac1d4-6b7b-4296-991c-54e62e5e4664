import { useState } from 'react'
import { Layout, Form, Radio, InputNumber, Select, Card, Divider, Checkbox, Button, message } from 'antd'
import './App.css'

const { Content } = Layout

interface PatientFormData {
  gender: 'M' | 'F'
  age: number
  height: number
  weight: number
  activityLevel: number
  hyperlipidemia: string // 高脂血症单选
  diabetes: boolean // 糖尿病复选框
}

function App() {
  const [form] = Form.useForm<PatientFormData>()
  const [formData, setFormData] = useState<PatientFormData | null>(null)

  const onFinish = (values: PatientFormData) => {
    console.log('表单数据:', values)
    setFormData(values)

    // 构建疾病标签数组用于显示
    const diseaseTags = []
    if (values.hyperlipidemia) {
      diseaseTags.push(`高脂血症${values.hyperlipidemia}`)
    }
    if (values.diabetes) {
      diseaseTags.push('糖尿病')
    }

    message.success(`表单提交成功！疾病标签: ${diseaseTags.join(', ')}`)
  }

  return (
    <Layout className="layout" style={{ width: '100%', minHeight: '100vh' }}>
      <Content style={{ padding: '40px', width: '100%' }}>
        <div style={{ display: 'flex', gap: '40px', width: '100%' }}>
          {/* 左侧表单 */}
          <Card 
            title="患者信息" 
            style={{ 
              flex: '0 0 500px',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              initialValues={{
                gender: 'M',
                activityLevel: 1,
                hyperlipidemia: '',
                diabetes: false
              }}
              style={{ padding: '0 20px' }}
            >
              <Form.Item
                label="性别"
                name="gender"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="M" style={{ marginBottom: '8px' }}>男</Radio>
                  <Radio value="F" style={{ marginBottom: '8px' }}>女</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="年龄"
                name="age"
                rules={[
                  { required: true, message: '请输入年龄' },
                  { type: 'number', min: 18, max: 150, message: '年龄必须在18-150岁之间' }
                ]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                label="身高（厘米）"
                name="height"
                rules={[
                  { required: true, message: '请输入身高' },
                  { type: 'number', min: 100, max: 250, message: '身高必须在100-250厘米之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={100}
                  max={250}
                />
              </Form.Item>

              <Form.Item
                label="体重（公斤）"
                name="weight"
                rules={[
                  { required: true, message: '请输入体重' },
                  { type: 'number', min: 20, max: 200, message: '体重必须在20-200公斤之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={20}
                  max={200}
                />
              </Form.Item>

              <Form.Item
                label="身体活动水平"
                name="activityLevel"
                rules={[
                  { required: true, message: '请输入身体活动水平' },
                  { type: 'number', min: 1, max: 3, message: '身体活动水平必须在1-3之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                  min={1}
                  max={3}
                />
              </Form.Item>

              <Divider orientation="left">疾病信息</Divider>

              <Form.Item
                label="高脂血症风险等级"
                name="hyperlipidemia"
                rules={[{ required: true, message: '请选择高脂血症风险等级' }]}
                tooltip="只能选择一个风险等级"
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="低危" style={{ marginBottom: '8px' }}>低危</Radio>
                  <Radio value="中危" style={{ marginBottom: '8px' }}>中危</Radio>
                  <Radio value="高危" style={{ marginBottom: '8px' }}>高危</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="合并症"
                name="diabetes"
                valuePropName="checked"
                tooltip="可以与高脂血症同时选择"
              >
                <div style={{ textAlign: 'left' }}>
                  <Checkbox>糖尿病</Checkbox>
                </div>
              </Form.Item>

              <Form.Item style={{ marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" size="large" block>
                  提交表单
                </Button>
              </Form.Item>
            </Form>
          </Card>

          {/* 右侧内容区域 */}
          <div style={{ flex: 1, minWidth: '500px' }}>
            {formData && (
              <Card
                title="表单数据预览"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{ padding: '0 20px', textAlign: 'left' }}>
                  <h4 style={{ textAlign: 'left' }}>基本信息</h4>
                  <p style={{ textAlign: 'left' }}><strong>性别:</strong> {formData.gender === 'M' ? '男' : '女'}</p>
                  <p style={{ textAlign: 'left' }}><strong>年龄:</strong> {formData.age} 岁</p>
                  <p style={{ textAlign: 'left' }}><strong>身高:</strong> {formData.height} 厘米</p>
                  <p style={{ textAlign: 'left' }}><strong>体重:</strong> {formData.weight} 公斤</p>
                  <p style={{ textAlign: 'left' }}><strong>身体活动水平:</strong> {formData.activityLevel}</p>

                  <Divider />

                  <h4 style={{ textAlign: 'left' }}>疾病信息</h4>
                  <p style={{ textAlign: 'left' }}><strong>高脂血症风险等级:</strong> {formData.hyperlipidemia || '未选择'}</p>
                  <p style={{ textAlign: 'left' }}><strong>糖尿病:</strong> {formData.diabetes ? '是' : '否'}</p>

                  <Divider />

                  <h4 style={{ textAlign: 'left' }}>选择规则说明</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>高脂血症风险等级：低危、中危、高危 <strong>只能选择一个</strong></li>
                    <li>糖尿病：<strong>可以与高脂血症同时选择</strong></li>
                    <li>这样的设计确保了高脂血症等级的互斥性，同时允许合并症的灵活选择</li>
                  </ul>
                </div>
              </Card>
            )}

            {!formData && (
              <Card
                title="使用说明"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{ padding: '0 20px', textAlign: 'left' }}>
                  <h4 style={{ textAlign: 'left' }}>疾病选择规则</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li><strong>高脂血症风险等级</strong>：低危、中危、高危三个选项互斥，只能选择一个</li>
                    <li><strong>糖尿病</strong>：作为合并症，可以与高脂血症同时选择</li>
                  </ul>

                  <h4 style={{ textAlign: 'left' }}>界面优化</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>使用 Radio 组件确保高脂血症选项的互斥性</li>
                    <li>使用 Checkbox 组件允许糖尿病的独立选择</li>
                    <li>添加分割线和工具提示增强用户体验</li>
                    <li>提交后在右侧显示选择结果</li>
                  </ul>
                </div>
              </Card>
            )}
          </div>
        </div>
      </Content>
    </Layout>
  )
}

export default App
