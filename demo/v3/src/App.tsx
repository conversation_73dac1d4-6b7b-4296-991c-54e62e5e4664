import {useState} from 'react'
import {<PERSON><PERSON>, Card, Checkbox, Divider, Form, InputNumber, Layout, message, Radio, Table, Typography } from 'antd'
import './App.css'

const { Content } = Layout
const { Title, Paragraph } = Typography

interface PatientFormData {
  gender: 'M' | 'F'
  age: number
  height: number
  weight: number
  activityLevel: number
  hyperlipidemia: string // 高脂血症单选
  diabetes: boolean // 糖尿病复选框
}

interface ReportData {
  patient: {
    gender: string
    age: number
    height: number
    weight: number
    pla: number
    tags: string[]
  }
  nutrition_metrics: {
    eer: number
    core: Array<{
      name: string
      unit: string
      amount: string
      ratio: string
    }>
    other: Array<{
      name: string
      unit: string
      amount: string
      food: string
    }>
  }
  nutrition: {
    overview: string
    structure: Array<{
      name: string
      items: Array<{
        type: string
        amount: number
        source: string
      }>
    }>
    note: string[]
  }
  exercise: {
    name: string
    overview: string
    goal: string
    components: Array<{
      type: string
      intensity: string
      frequency: string
      duration: string
      recommend: string
      guidance: string
      notes: string
    }>
    avoid: string[]
    overall: string[]
  }
  screening: {
    overall: string
    regular_items: Array<{
      name: string
      period: string
      goal: string | null
    }>
    regular_note: string
    as_needed: string
    note: string
  }
}

function App() {
  const [form] = Form.useForm<PatientFormData>()
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(false)

  const generateReport = async (formData: PatientFormData): Promise<ReportData> => {
    // 构建请求数据
    const requestData = {
      gender: formData.gender,
      age: formData.age,
      height: formData.height / 100, // 转换为米
      weight: formData.weight,
      pla: formData.activityLevel,
      tags: [
        ...(formData.hyperlipidemia ? [`高脂血症${formData.hyperlipidemia}`] : []),
        ...(formData.diabetes ? ['糖尿病'] : [])
      ]
    }

    console.log('发送请求数据:', requestData)

    try {
      // 调用后端接口
      const response = await fetch('http://127.0.0.1:8000/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      console.log(response)

      return await response.json()
    } catch (error) {
      console.error('接口调用失败:', error)
      throw error
    }
  }

  const onFinish = async (values: PatientFormData) => {
    console.log('表单数据:', values)
    setLoading(true)

    try {
      const report = await generateReport(values)
      setReportData(report)
      message.success('体检报告生成成功！')
    } catch (error) {
      message.error('生成报告失败，请稍后重试')
      console.error('生成报告失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout className="layout" style={{ width: '100%', minHeight: '100vh' }}>
      <Content style={{ padding: '40px', width: '100%' }}>
        <div style={{ display: 'flex', gap: '40px', width: '100%' }}>
          {/* 左侧表单 */}
          <Card 
            title="患者信息" 
            style={{ 
              flex: '0 0 500px',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
            }}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              initialValues={{
                gender: 'M',
                activityLevel: 1.0,
                hyperlipidemia: '',
                diabetes: false
              }}
              style={{ padding: '0 20px' }}
            >
              <Form.Item
                label="性别"
                name="gender"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="M" style={{ marginBottom: '8px' }}>男</Radio>
                  <Radio value="F" style={{ marginBottom: '8px' }}>女</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="年龄"
                name="age"
                rules={[
                  { required: true, message: '请输入年龄' },
                  { type: 'number', min: 18, max: 150, message: '年龄必须在18-150岁之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={18}
                  max={150}
                />
              </Form.Item>

              <Form.Item
                label="身高（厘米）"
                name="height"
                rules={[
                  { required: true, message: '请输入身高' },
                  { type: 'number', min: 100, max: 250, message: '身高必须在100-250厘米之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={100}
                  max={250}
                />
              </Form.Item>

              <Form.Item
                label="体重（公斤）"
                name="weight"
                rules={[
                  { required: true, message: '请输入体重' },
                  { type: 'number', min: 20, max: 200, message: '体重必须在20-200公斤之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={1}
                  precision={0}
                  min={20}
                  max={200}
                />
              </Form.Item>

              <Form.Item
                label="身体活动水平"
                name="activityLevel"
                rules={[
                  { required: true, message: '请输入身体活动水平' },
                  { type: 'number', min: 1, max: 3, message: '身体活动水平必须在1-3之间' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  step={0.1}
                  precision={1}
                  min={1}
                  max={3}
                />
              </Form.Item>

              <Divider orientation="left">疾病信息</Divider>

              <Form.Item
                label="高脂血症风险等级"
                name="hyperlipidemia"
                rules={[{ required: true, message: '请选择高脂血症风险等级' }]}
                tooltip="只能选择一个风险等级"
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Radio value="低危" style={{ marginBottom: '8px' }}>低危</Radio>
                  <Radio value="中危" style={{ marginBottom: '8px' }}>中危</Radio>
                  <Radio value="高危" style={{ marginBottom: '8px' }}>高危</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="合并症"
                name="diabetes"
                valuePropName="checked"
                tooltip="可以与高脂血症同时选择"
              >
                <div style={{ textAlign: 'left' }}>
                  <Checkbox>糖尿病</Checkbox>
                </div>
              </Form.Item>

              <Form.Item style={{ marginTop: '24px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loading}
                >
                  {loading ? '生成报告中...' : '生成体检报告'}
                </Button>
              </Form.Item>
            </Form>
          </Card>

          {/* 右侧内容区域 */}
          <div style={{ flex: 1, minWidth: '500px' }}>
            {reportData && (
              <Card
                title="个性化健康管理报告"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                  maxHeight: '90vh',
                  overflow: 'auto'
                }}
              >
                <div style={{ padding: '0 20px', textAlign: 'left' }}>

                  <Title level={3}>一、营养方案</Title>

                  <Title level={4}>（一）膳食原则及目标</Title>
                  <div>
                    {reportData.nutrition.overview.split('。').map((paragraph, index) => (
                      <p key={index} style={{ marginBottom: '12px' }}>{paragraph}</p>
                    ))}
                  </div>

                  <Title level={4}>（二）一日膳食能量需要量</Title>
                  <p>您的每日膳食能量摄入范围为 {reportData.nutrition_metrics.eer} 千卡</p>

                  <Title level={4}>（三）一日三餐营养素建议</Title>
                  <Table
                    dataSource={reportData.nutrition_metrics.core.map((item, index) => ({
                      key: index,
                      nutrient: item.name,
                      amount: item.amount + item.unit,
                      ratio: item.ratio
                    }))}
                    columns={[
                      {
                        title: '营养素',
                        dataIndex: 'nutrient',
                        key: 'nutrient',
                        width: '30%'
                      },
                      {
                        title: '推荐量（g）',
                        dataIndex: 'amount',
                        key: 'amount',
                        width: '30%'
                      },
                      {
                        title: '占总能量比（%）',
                        dataIndex: 'ratio',
                        key: 'ratio',
                        width: '40%'
                      }
                    ]}
                    pagination={false}
                    size="small"
                    style={{ marginBottom: '16px' }}
                  />

                  <Title level={4}>（四）每日非功能营养素摄入建议</Title>
                  <Table
                    dataSource={reportData.nutrition_metrics.other.map((item, index) => ({
                      key: index,
                      nutrient: item.name,
                      amount: `${item.amount} ${item.unit}`,
                      food: item.food
                    }))}
                    columns={[
                      {
                        title: '营养素',
                        dataIndex: 'nutrient',
                        key: 'nutrient',
                        width: '30%'
                      },
                      {
                        title: '推荐量',
                        dataIndex: 'amount',
                        key: 'amount',
                        width: '30%'
                      },
                      {
                        title: '推荐食物',
                        dataIndex: 'food',
                        key: 'food',
                        width: '40%'
                      }
                    ]}
                    // pagination={{ position: ['bottomLeft'], pageSize: 10 }}
                    pagination={false}
                    size="small"
                    style={{ marginBottom: '16px' }}
                  />

                  <Title level={4}>（五）一日三餐膳食结构建议</Title>
                  {reportData.nutrition.structure.map((meal, index) => (
                    <div key={index}>
                      <Title level={5}>{meal.name}:</Title>
                      <ul style={{ paddingLeft: '20px', marginTop: '4px' }}>
                        {meal.items.map((item, itemIndex) => (
                          <li key={itemIndex}>{item.type}: {item.amount}g ({item.source})</li>
                        ))}
                      </ul>
                    </div>
                  ))}

                  <Title level={4}>（六）其它注意事项</Title>
                  {reportData.nutrition.note.map((note, index) => (
                      <Paragraph key={index} style={{ marginBottom: '8px' }}>{index+1}. {note}</Paragraph>
                  ))}

                  <Divider />

                  <Title level={3}>二、运动方案</Title>
                  {reportData.exercise.name}
                  <div>
                    {reportData.exercise.overview.split('。').map((paragraph, index) => (
                      <p key={index} style={{ marginBottom: '12px' }}>{paragraph}</p>
                    ))}
                  </div>

                  <Title level={4}>（一）运动原则及目标</Title>
                  {reportData.exercise.goal.split('。').map((paragraph, index) => (
                      <p key={index} style={{ marginBottom: '12px' }}>{paragraph}</p>
                    ))}

                  <Title level={4}>（二）运动建议</Title>
                  {reportData.exercise.components.map((component, index) => (
                    <>
                      <Title level={5}>{component.type}</Title>
                      <Paragraph>运动强度：{component.intensity}</Paragraph>
                      <Paragraph>运动频率：{component.frequency}</Paragraph>
                      <Paragraph>运动时长：{component.duration}</Paragraph>
                      <Paragraph>运动方式：{component.recommend}</Paragraph>
                      <Paragraph>运动方式指导：{component.guidance}</Paragraph>
                      <Paragraph>注意事项：{component.notes}</Paragraph>
                    </>
                  ))}

                  <Title level={4}>（三）需要避免的运动</Title>
                  {reportData.exercise.avoid.map((item, index) => (
                    <Paragraph key={index} style={{ marginBottom: '8px' }}>{index+1}. {item}</Paragraph>
                  ))}

                  <Title level={4}>（四）总体注意事项</Title>
                  {reportData.exercise.overall.map((item, index) => (
                    <Paragraph key={index} style={{ marginBottom: '8px' }}>{index+1}. {item}</Paragraph>
                  ))}

                  <Divider />

                  <Title level={3}>三、筛查方案</Title>
                  <Paragraph>{reportData.screening.overall}</Paragraph>

                  <Title level={4}>（一）定期随访项目</Title>
                  <Table
                    dataSource={reportData.nutrition_metrics.regular_items.map((item, index) => ({
                      key: index,
                      nutrient: item.name,
                      amount: item.amount + item.unit,
                      ratio: item.ratio
                    }))}
                    columns={[
                      {
                        title: '项目名称',
                        dataIndex: 'name',
                        key: 'name',
                        width: '30%'
                      },
                      {
                        title: '随访周期',
                        dataIndex: 'period',
                        key: 'period',
                        width: '30%'
                      },
                      {
                        title: '管理目标',
                        dataIndex: 'goal',
                        key: 'goal',
                        width: '40%'
                      }
                    ]}
                    pagination={false}
                    size="small"
                    style={{ marginBottom: '16px' }}
                  />
                  <Paragraph>{reportData.screening.regular_note}</Paragraph>

                  <Title level={4}>（二）不适随诊症状</Title>
                  <Paragraph>{reportData.screening.as_needed}</Paragraph>

                  <Title level={4}>（三）温馨提醒</Title>
                  <Paragraph>{reportData.screening.note}</Paragraph>
                </div>
              </Card>
            )}

            {!reportData && !loading && (
              <Card
                title="使用说明"
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              >
                <div style={{ padding: '0 20px', textAlign: 'left' }}>
                  <h4 style={{ textAlign: 'left' }}>填写表单生成个性化健康管理报告</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li>填写完整的患者基本信息</li>
                    <li>选择高脂血症风险等级（低危/中危/高危）</li>
                    <li>选择是否患有糖尿病</li>
                    <li>点击"生成体检报告"按钮</li>
                  </ul>

                  <h4 style={{ textAlign: 'left' }}>报告内容包括</h4>
                  <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                    <li><strong>营养方案</strong>：个性化膳食建议和营养指标</li>
                    <li><strong>运动方案</strong>：针对性的运动处方和注意事项</li>
                    <li><strong>筛查方案</strong>：定期检查项目和健康监测</li>
                  </ul>
                </div>
              </Card>
            )}

            {loading && (
              <Card
                title="正在生成报告..."
                style={{
                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ padding: '40px' }}>
                  <p>正在根据您的信息生成个性化健康管理报告，请稍候...</p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </Content>
    </Layout>
  )
}

export default App
