import { useState } from 'react'
import {
  Form,
  Radio,
  InputNumber,
  Select,
  Card,
  Button,
  Typography,
  Divider,
  message,
  Table,
  Row,
  Col
} from 'antd'
import './App.css'

const { Title, Paragraph } = Typography

interface PatientFormData {
  gender: 'M' | 'F'
  age: number
  height: number
  weight: number
  activityLevel: number
  diseaseTags: string[]
}

interface ReportData {
  patient: {
    gender: string
    age: number
    height: number
    weight: number
    pla: number
    tags: string[]
  }
  nutrition_metrics: {
    eer: number
    core: Array<{
      name: string
      unit: string
      amount: string
      ratio: string
    }>
    other: Array<{
      name: string
      unit: string
      amount: string
      food: string
    }>
  }
  nutrition: {
    overview: string
    structure: Array<{
      name: string
      items: Array<{
        type: string
        amount: number
        source: string
      }>
    }>
    note: string[]
  }
  exercise: {
    name: string
    overview: string
    goal: string
    components: Array<{
      type: string
      intensity: string
      frequency: string
      duration: string
      recommend: string
      guidance: string
      notes: string
    }>
    avoid: string[]
    overall: string[]
  }
  screening: {
    overall: string
    regular_items: Array<{
      name: string
      period: string
      goal: string | null
    }>
    regular_note: string
    as_needed: string
    note: string
  }
}

function App() {

  const [form] = Form.useForm<PatientFormData>()
  const [selectedDiseaseTags, setSelectedDiseaseTags] = useState<string[]>([])
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(false)

  const handleDiseaseTagsChange = (value: string[]) => {
    const highRiskTags = ['高脂血症低危', '高脂血症中危', '高脂血症高危']
    const diabetesTag = '糖尿病'

    // 分离高脂血症标签和糖尿病标签
    const newHighRiskSelections = value.filter(tag => highRiskTags.includes(tag))
    const hasDiabetes = value.includes(diabetesTag);

    const nextSelectedTags: string[] = [];

    // 处理高脂血症标签（只保留最后选择的一个）
    if (newHighRiskSelections.length > 0) {
      const lastSelectedHighRisk = newHighRiskSelections[newHighRiskSelections.length - 1]
      nextSelectedTags.push(lastSelectedHighRisk);
    }

    // 如果选择了糖尿病标签，添加到结果中
    if (hasDiabetes) {
      nextSelectedTags.push(diabetesTag);
    }

    setSelectedDiseaseTags(nextSelectedTags);
  }

  // 添加生成报告函数 (示例，需要根据实际后端接口调整)
  const generateReport = async (formData: PatientFormData): Promise<ReportData> => {
    // 构建请求数据
    const requestData = {
      gender: formData.gender,
      age: formData.age,
      height: formData.height, // 注意：如果后端需要厘米，这里需要 * 100
      weight: formData.weight,
      pla: formData.activityLevel,
      tags: formData.diseaseTags // 直接使用处理后的疾病标签
    }

    console.log('发送请求数据:', requestData)

    try {
      // 调用后端接口
      // 请将这里的 URL 替换为您的实际后端接口地址
      const response = await fetch('http://127.0.0.1:8000/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const report = await response.json()
      return report;

    } catch (error) {
      console.error('接口调用失败:', error)
      throw error
    }
  }


  const onFinish = async (values: PatientFormData) => {

    // 身高由厘米转为米
    const heightInMeter = values.height / 100;

    // 在这里可以将 selectedDiseaseTags 的值赋给 values.diseaseTags
    // 确保提交的数据包含处理后的疾病标签
    const dataToSubmit = { ...values, height: heightInMeter, diseaseTags: selectedDiseaseTags };

    // 开始生成新报告时，先清空之前的报告数据
    setReportData(null)
    setLoading(true)

    try {
      const report = await generateReport(dataToSubmit);
      setReportData(report);
      message.success('报告生成成功！');
    } catch (error) {
      message.error('生成报告失败，请稍后重试');
      console.error('生成报告失败:', error);
    } finally {
      setLoading(false);
    }
  }

  return (
    <Row>
      <Col span={24} lg={6}>
        <Card title="患者信息" className='sticky-on-desktop'>
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish} // 绑定提交事件
            initialValues={{
              gender: 'M',
              age: 68,
              height: 178,
              weight: 68,
              activityLevel: 1.6
            }}
            style={{ padding: '0 20px' }}
            className="patient-form-vertical-form"
          >
            <Form.Item
              label="性别"
              name="gender"
              rules={[{ required: true, message: '请选择性别' }]}
            >
              <Radio.Group style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <Radio value="M">男</Radio>
                <Radio value="F">女</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="年龄"
              name="age"
              rules={[
                { required: true, message: '请输入年龄' },
                { type: 'number', min: 18, max: 150, message: '年龄必须在18-150岁之间' }
              ]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="身高（厘米）"
              name="height"
              rules={[
                { required: true, message: '请输入身高' },
                { type: 'number', min: 100, max: 250, message: '身高必须在100-250厘米之间' }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                step={1}
                precision={0}
              />
            </Form.Item>

            <Form.Item
              label="体重（公斤）"
              name="weight"
              rules={[
                { required: true, message: '请输入体重' },
                { type: 'number', min: 20, max: 200, message: '体重必须在20-200公斤之间' }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                step={0.1}
                precision={1}
              />
            </Form.Item>

            <Form.Item
              label="身体活动水平"
              name="activityLevel"
              rules={[
                { required: true, message: '请输入身体活动水平' },
                { type: 'number', min: 0.5, max: 3, message: '身体活动水平必须在0.5-3之间' }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                step={0.1}
                precision={1}
              />
            </Form.Item>

            <Form.Item
              label="疾病标签"
              name="diseaseTags"
              rules={[{ required: true, message: '请选择疾病标签' }]}
            >
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="请选择疾病标签"
                onChange={handleDiseaseTagsChange}
                value={selectedDiseaseTags}
                key={JSON.stringify(selectedDiseaseTags)}
              >
                <Select.OptGroup label="高脂血症">
                  <Select.Option value="高脂血症低危" disabled={selectedDiseaseTags.some(tag => ['高脂血症中危','高脂血症高危'].includes(tag))}>
                    高脂血症低危
                  </Select.Option>
                  <Select.Option value="高脂血症中危" disabled={selectedDiseaseTags.some(tag => ['高脂血症低危','高脂血症高危'].includes(tag))}>
                    高脂血症中危
                  </Select.Option>
                  <Select.Option value="高脂血症高危" disabled={selectedDiseaseTags.some(tag => ['高脂血症低危','高脂血症中危'].includes(tag))}>
                    高脂血症高危
                  </Select.Option>
                </Select.OptGroup>
                <Select.OptGroup label="合并症">
                  <Select.Option value="糖尿病">糖尿病</Select.Option>
                </Select.OptGroup>
              </Select>
            </Form.Item>

            {/* 添加提交按钮 */}
            <Form.Item style={{ marginTop: '24px' }}>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                block
                loading={loading}
              >
                {loading ? '生成报告中...' : '生成健康报告'}
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </Col>
      <Col span={24} lg={18}>
        {!reportData && !loading && (
          <Card title="使用说明">
            <div style={{ padding: '0 20px', textAlign: 'left', marginBottom: 0, paddingBottom: 0 }}>
              <h4 style={{ textAlign: 'left' }}>填写表单生成个性化健康管理报告</h4>
              <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                <li>填写完整的患者基本信息</li>
                <li>选择疾病标签（高脂血症、糖尿病）</li>
                <li>点击"生成健康报告"按钮</li>
              </ul>

              <h4 style={{ textAlign: 'left' }}>报告内容包括</h4>
              <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                <li><strong>营养方案</strong>：个性化膳食建议和营养指标</li>
                <li><strong>运动方案</strong>：针对性的运动处方和注意事项</li>
                <li><strong>筛查方案</strong>：定期检查项目和健康监测</li>
              </ul>
            </div>
          </Card>
        )}

        {loading && (
          <Card
            title="正在生成报告..."
            style={{
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
              textAlign: 'center',
              marginBottom: 0,
              paddingBottom: 0
            }}
          >
            <div style={{ padding: '40px', marginBottom: 0, paddingBottom: 0 }}>
              <p>正在根据您的信息生成个性化健康管理报告，请稍候...</p>
            </div>
          </Card>
        )}

        {reportData && (
          <Card
            title="个性化健康管理报告"
            style={{
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
              marginBottom: 0,
              paddingBottom: 0
            }}
          >
            <div className="report-content" style={{ padding: '0 20px', textAlign: 'left', marginBottom: 0, paddingBottom: 0 }}>

              <Title level={3}>一、营养方案</Title>

              <Title level={4}>（一）膳食原则及目标</Title>
              <div>
                {reportData.nutrition.overview.split('。').filter(Boolean).map((paragraph, idx) => (
                  <p key={paragraph + idx} style={{ marginBottom: '12px' }}>{paragraph}</p>
                ))}
              </div>

              <Title level={4}>（二）一日膳食能量需要量</Title>
              <p>您的每日膳食能量摄入范围为 {reportData.nutrition_metrics.eer} 千卡</p>

              <Title level={4}>（三）一日三餐营养素建议</Title>
              <Table
                dataSource={reportData.nutrition_metrics.core.map((item) => ({
                  key: item.name,
                  nutrient: item.name,
                  amount: item.amount + item.unit,
                  ratio: item.ratio
                }))}
                columns={[
                  {
                    title: '营养素',
                    dataIndex: 'nutrient',
                    key: 'nutrient',
                    width: 100
                  },
                  {
                    title: '推荐量（g）',
                    dataIndex: 'amount',
                    key: 'amount',
                    width: 120
                  },
                  {
                    title: '占总能量比（%）',
                    dataIndex: 'ratio',
                    key: 'ratio',
                    width: 130
                  }
                ]}
                pagination={false}
                size="small"
                scroll={{ x: 350 }}
                style={{ marginBottom: '12px' }}
              />

              <Title level={4}>（四）每日非功能营养素摄入建议</Title>
              <Table
                dataSource={reportData.nutrition_metrics.other.map((item) => ({
                  key: item.name,
                  nutrient: item.name,
                  amount: `${item.amount} ${item.unit}`,
                  food: item.food
                }))}
                columns={[
                  {
                    title: '营养素',
                    dataIndex: 'nutrient',
                    key: 'nutrient',
                    width: 100
                  },
                  {
                    title: '推荐量',
                    dataIndex: 'amount',
                    key: 'amount',
                    width: 100
                  },
                  {
                    title: '推荐食物',
                    dataIndex: 'food',
                    key: 'food',
                    width: 200
                  }
                ]}
                pagination={false}
                size="small"
                scroll={{ x: 400 }}
                style={{ marginBottom: '16px' }}
              />

              <Title level={4}>（五）一日三餐膳食结构建议</Title>
              {reportData.nutrition.structure.map((meal) => (
                <div key={meal.name}>
                  <Title level={5}>{meal.name}:</Title>
                  <ul style={{ paddingLeft: '20px', marginTop: '4px' }}>
                    {meal.items.map((item) => (
                      <li key={item.type + '-' + item.source}>{item.type}: {item.amount}g ({item.source})</li>
                    ))}
                  </ul>
                </div>
              ))}

              <Title level={4}>（六）其它注意事项</Title>
              {reportData.nutrition.note.map((note, idx) => (
                <Paragraph key={note + idx} style={{ marginBottom: '8px' }}>{idx+1}. {note}</Paragraph>
              ))}

              <Divider />

              <Title level={3}>二、运动方案</Title>
              <Paragraph>{reportData.exercise.name}</Paragraph>
              <div>
                {reportData.exercise.overview.split('。').filter(Boolean).map((paragraph, idx) => (
                  <p key={paragraph + idx} style={{ marginBottom: '12px' }}>{paragraph}</p>
                ))}
              </div>

              <Title level={4}>（一）运动原则及目标</Title>
              {reportData.exercise.goal.split('。').filter(Boolean).map((paragraph, idx) => (
                <p key={paragraph + idx} style={{ marginBottom: '12px' }}>{paragraph}</p>
              ))}

              <Title level={4}>（二）运动建议</Title>
              {reportData.exercise.components.map((component) => (
                <div key={component.type + '-' + component.intensity + '-' + component.frequency} className="exercise-component" style={{ marginBottom: '12px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                  <Title level={5}>{component.type}</Title>
                  <Paragraph>运动强度：{component.intensity}</Paragraph>
                  <Paragraph>运动频率：{component.frequency}</Paragraph>
                  <Paragraph>运动时长：{component.duration}</Paragraph>
                  <Paragraph>运动方式：{component.recommend}</Paragraph>
                  <Paragraph>运动方式指导：{component.guidance}</Paragraph>
                  <Paragraph>注意事项：{component.notes}</Paragraph>
                </div>
              ))}

              <Title level={4}>（三）需要避免的运动</Title>
              {reportData.exercise.avoid.map((item, idx) => (
                <Paragraph key={item + idx} style={{ marginBottom: '8px' }}>{idx+1}. {item}</Paragraph>
              ))}

              <Title level={4}>（四）总体注意事项</Title>
              {reportData.exercise.overall.map((item, idx) => (
                <Paragraph key={item + idx} style={{ marginBottom: '8px' }}>{idx+1}. {item}</Paragraph>
              ))}

              <Divider />

              <Title level={3}>三、筛查方案</Title>
              <Paragraph>{reportData.screening.overall}</Paragraph>

              <Title level={4}>（一）定期随访项目</Title>
              <Table
                dataSource={reportData.screening.regular_items.map(item => ({
                  ...item,
                  key: item.name + (item.period || '') + (item.goal || '')
                }))}
                columns={[
                  {
                    title: '项目名称',
                    dataIndex: 'name',
                    key: 'name',
                    width: 150
                  },
                  {
                    title: '随访周期',
                    dataIndex: 'period',
                    key: 'period',
                    width: 100
                  },
                  {
                    title: '管理目标',
                    dataIndex: 'goal',
                    key: 'goal',
                    width: 150
                  }
                ]}
                pagination={false}
                size="small"
                scroll={{ x: 400 }}
                style={{ marginBottom: '16px' }}
              />
              <Paragraph>{reportData.screening.regular_note}</Paragraph>

              <Title level={4}>（二）不适随诊症状</Title>
              <Paragraph>{reportData.screening.as_needed}</Paragraph>

              <Title level={4}>（三）温馨提醒</Title>
              <Paragraph>{reportData.screening.note}</Paragraph>
            </div>
          </Card>
        )}
      </Col>
    </Row>
  )
}

export default App