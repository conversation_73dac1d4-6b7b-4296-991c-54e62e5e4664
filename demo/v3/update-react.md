# React 18 降级指南

## 📋 已修改的文件

### package.json 更新
- `react`: `^19.1.0` → `^18.3.1`
- `react-dom`: `^19.1.0` → `^18.3.1`
- `@types/react`: `^19.1.2` → `^18.3.12`
- `@types/react-dom`: `^19.1.2` → `^18.3.1`

## 🚀 安装步骤

### 1. 删除现有依赖
```bash
cd demo/v3
rm -rf node_modules
rm package-lock.json  # 如果使用npm
rm yarn.lock          # 如果使用yarn
rm pnpm-lock.yaml     # 如果使用pnpm
```

### 2. 安装新依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 3. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

## ✅ 版本信息

### React 18.3.1 特性
- **稳定版本**：React 18 的最新稳定版本
- **完全兼容 Antd v5**：解决兼容性警告
- **性能优化**：包含所有 React 18 的性能改进
- **并发特性**：支持 Suspense、Concurrent Rendering 等

### 兼容性确认
- ✅ Antd v5.25.3 完全支持 React 18
- ✅ TypeScript 配置兼容
- ✅ Vite 构建工具兼容
- ✅ 所有现有代码无需修改

## 🔧 如果遇到问题

### 清理缓存
```bash
# npm
npm cache clean --force

# yarn
yarn cache clean

# pnpm
pnpm store prune
```

### 重新安装
```bash
rm -rf node_modules
npm install
```

## 📝 注意事项

1. **React 18 vs React 19**：React 18.3.1 是当前最稳定的版本
2. **Antd 兼容性**：完全解决了 Antd v5 的兼容性警告
3. **代码无需修改**：现有的 JSX 和组件代码完全兼容
4. **性能提升**：React 18 的并发特性提供更好的用户体验
