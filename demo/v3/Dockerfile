# ===== 构建阶段 =====
FROM node:20-alpine AS builder

# 安装 pnpm
RUN npm install -g pnpm

# 创建并切换到应用目录
WORKDIR /app

# 复制依赖相关文件
COPY package.json pnpm-lock.yaml* ./

# 安装依赖
RUN pnpm install --registry=https://registry.npmmirror.com

# 复制项目代码
COPY . .

# 运行构建
RUN npm run build

# ===== 运行阶段（使用 nginx 提供静态服务）=====
FROM nginx:alpine

# 清理默认 nginx 页面
RUN rm -rf /usr/share/nginx/html/*

# 拷贝构建好的前端文件到 nginx 默认目录
COPY --from=builder /app/dist /usr/share/nginx/html/

# 拷贝自定义 nginx 配置，覆盖默认的 default.conf
COPY default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
