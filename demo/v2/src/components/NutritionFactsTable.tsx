import * as React from "react";
import {Table} from "antd";
import type {TableProps} from 'antd';

type DataType = {
  nutrient: string,
  amount: number,
  ratio: number
}

type Prop = {
  dataSource: DataType[],
}

const NutritionFactsTable: React.FC<Prop> = (props) => {

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '营养素',
      dataIndex: 'nutrient',
      align: 'center'
    },
    {
      title: '推荐量（g）',
      dataIndex: 'amount',
      align: 'center'
    },
    {
      title: '占总能量比（%）',
      dataIndex: 'ratio',
      align: 'center'
    },
  ]
  return <Table
    className="w-[50%]"
    size="small"
    bordered
    columns={columns}
    dataSource={props.dataSource}
    pagination={false}
  />
}

export default NutritionFactsTable