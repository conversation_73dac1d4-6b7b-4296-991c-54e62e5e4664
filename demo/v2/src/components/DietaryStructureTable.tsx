import * as React from "react";
import {Table} from "antd";
import type {TableProps} from 'antd';
import {useMemo} from "react";

type StructureItem = {
  type: string,
  amount: number,
  source: string
}

type Structure = {
  name: string,
  items: StructureItem[],
}

type DataSource = {
  name: string,
  type: string,
  amount: number,
  source: string
}

type Prop = {
  structureData: Structure[],
}

const DietaryStructureTable: React.FC<Prop> = (props) => {
  const {structureData} = props
  const dataSource = useMemo(() => {
    const data: DataSource[] = []
    const mergeMap = new Map()
    structureData.forEach(structure => {
      mergeMap.set(data.length, structure.items.length)
      structure.items.forEach(item => {
        data.push({
          name: structure.name,
          type: item.type,
          amount: item.amount,
          source: item.source,
        })
      })
    })
    return {data, mergeMap}
  }, [structureData])

  const columns: TableProps<DataSource>['columns'] = [
    {
      title: '',
      dataIndex: 'name',
      align: 'center',
      onCell: (_, index) => ({
        rowSpan: dataSource.mergeMap.get(index) ? dataSource.mergeMap.get(index) : 0,
      }),
    },
    {
      title: '食物种类',
      dataIndex: 'type',
      align: 'center'
    },
    {
      title: '推荐量（g）',
      dataIndex: 'amount',
      align: 'center'
    },
    {
      title: '食物来源',
      dataIndex: 'source',
      align: 'center',
    },
  ]
  return <Table
    className="w-[550px]"
    bordered
    size="small"
    columns={columns}
    dataSource={dataSource.data}
    pagination={false}
  />
}

export default DietaryStructureTable