import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import jsonData from "@/utils/generate.json";
import NutritionFactsTable from "@/components/NutritionFactsTable.tsx";
import DietaryStructureTable from "@/components/DietaryStructureTable.tsx";
import * as React from "react";

type Prop = {
  dataSource: typeof jsonData,
}

const SolutionDetails: React.FC<Prop> = (props) => {
  const {dataSource} = props

  return <Card>
    <CardHeader>
      <CardTitle>健康管理方案</CardTitle>
    </CardHeader>
    <ScrollArea className="h-[85vh]">
      <CardContent className="text-sm/6">
        <h4 className="font-semibold text-sm/8">1、营养方案</h4>
        <h4 className="indent-6 font-semibold text-sm/8">（1）膳食原则及目标</h4>
        <p className="indent-8">{dataSource.nutrition.overview}</p>

        <h4 className="indent-6 font-semibold text-sm/8">（2）一日膳食能量需要量</h4>
        <p className="indent-8 whitespace-pre-wrap">您的每日膳食能量摄入范围为{dataSource.nutrition.intake}kcal。</p>
        <p className="indent-8">数据来源：sheet 模块-营养-正常-膳食能量需要量。</p>

        <h4 className="indent-6 font-semibold text-sm/8">（3）一日三餐营养素建议</h4>
        <div className="ml-8">
          <NutritionFactsTable dataSource={dataSource.nutrition.meal}/>
        </div>
        <p className="indent-8">数据来源：sheet 模块-营养-正常-营养素目标。</p>

        <h4 className="indent-6 font-semibold text-sm/8">（4）一日三餐膳食结构建议</h4>
        <div className="ml-8">
          <DietaryStructureTable structureData={dataSource.nutrition.structure}/>
        </div>
        <p className="indent-8">数据来源：sheet 模块-营养-正常-确定和选择食物；食物成分表（待pdf转excel）。</p>
        <h4 className="indent-6 font-semibold text-sm/8">（5）其他注意事项</h4>
        <p className="indent-8">{dataSource.nutrition.note}</p>

        <h4 className="font-semibold text-sm/8">2、运动方案</h4>
        <h4 className="indent-6 font-semibold text-sm/8">（1）运动原则及目标</h4>
        <p className="indent-8">{dataSource.exercise.overview}</p>
        {dataSource.exercise.components.map((item, index) => (
          <>
            <h4 className="indent-6 font-semibold text-sm/8">（{index + 2}）{item.type}</h4>
            <p className="indent-8">运动方式：{item.recommend}</p>
            <p className="indent-8">运动强度：{item.intensity}{item.guidance ? `（${item.guidance}）` : ''}</p>
            <p className="indent-8">运动频率：{item.frequency}</p>
            <p className="indent-8">运动时长：{item.duration}</p>
            <p className="indent-8">运动进阶：{item.progression}</p>
            <p className="indent-8">运动方式指导：{item.guidance}</p>
            <p className="indent-8">注意事项：{item.notes || '暂无'}</p>
          </>
        ))}
      </CardContent>
    </ScrollArea>
  </Card>
}

export default SolutionDetails;