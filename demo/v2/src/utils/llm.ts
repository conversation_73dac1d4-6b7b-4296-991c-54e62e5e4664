import OpenAI from "openai";

const openai = new OpenAI({
    apiKey: 'sk-e050b9d5559845099260622cfda9b561',
    baseURL: "http://192.168.1.12:11435/v1",
    dangerouslyAllowBrowser: true,
});

export const getStrategy = async (): Promise<string> => {
    const nutritions: string[] = [];
    const exercises: string[] = [];
    const completion = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
            {"role": "system", "content": "你是一名专业的营养师，根据用户提供的营养建议和运动建议，给出一份详细的饮食和运动计划。"},
            {"role": "user", "content": getTemplate(nutritions.map(n => '- ' + n).join('\n'), exercises.map(e => '- ' + e).join('\n'))}
        ],
        stream: true,
    });

    let fullContent = "";
    for await (const chunk of completion) {
        if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
            fullContent = fullContent + (chunk.choices[0].delta.content || "");
        }
    }
    return (fullContent ? ('\n' + fullContent) : '');
}

const getTemplate = (nutritions: string, exercises: string) => {
    const template = `请根据以下的营养和运动建议，给出一份详细的饮食和运动计划（周一至周日）。
        营养建议：
        ${nutritions}
        运动建议：
        ${exercises}`
    console.log(template);
    return template
}