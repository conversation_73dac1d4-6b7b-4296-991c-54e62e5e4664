import './App.css'
import {<PERSON>, CardContent, CardHeader, CardTitle} from './components/ui/card'
import jsonData from './utils/generate.json'
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import NutritionFactsTable from "@/components/NutritionFactsTable.tsx";
import DietaryStructureTable from "@/components/DietaryStructureTable.tsx";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar.tsx";
import {ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger} from './components/ui/context-menu';
import {useState} from "react";
import SolutionDetails from "@/components/SolutionDetails.tsx";

function App() {
  const [currentUser, setCurrentUser] = useState<unknown>()
  const [loading, setLoading] = useState(false)

  const generateScheme = async () => {
    console.log('生成方案')
    setCurrentUser('')
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 5000));
    setLoading(false)
  }

  return (
    <div className="flex gap-2 p-2 ">
      <Card className="w-[300px] shrink-0 text-sm/6">
        <CardHeader>
          <CardTitle>列表</CardTitle>
        </CardHeader>
        <CardContent>
          <ContextMenu>
            <ContextMenuTrigger>
              <div className="flex items-center border-b-1 p-2 my-1">
                <Avatar>
                  <AvatarImage src="https://github.com/shadcn.png" />
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
                <p className="ml-3">张三</p>
              </div>
            </ContextMenuTrigger>
            <ContextMenuContent>
              <ContextMenuItem onSelect={() => generateScheme()}>生成方案</ContextMenuItem>
            </ContextMenuContent>
          </ContextMenu>

        </CardContent>
      </Card>
      <SolutionDetails dataSource={jsonData} />
    </div>
  )
}

export default App
