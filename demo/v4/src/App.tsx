import { Card } from "antd";
import { ProForm, ProFormCheckbox, ProFormDigit, ProFormRadio } from "@ant-design/pro-components"

function App() {

  const onFinish = async (values: any) => {
    console.log(values);
  }

  return (
      <div className="grid grid-cols-24 gap-4 px-2 md:px-4 pt-2 md:pt-4 items-start">
        <Card
            title="患者信息"
            className="col-span-24 md:col-span-8 shadow-lg sticky top-0 z-10"
        >
          <ProForm
              layout="horizontal"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 16 }}
              submitter={{
                    render: (_, doms) => (
                        <div className="grid grid-cols-24">
                          <div className="col-start-6 col-span-16">
                            <div className="flex gap-4">{doms}</div>
                          </div>
                        </div>
                    )
            }}
            onFinish={onFinish}
            >
            <ProFormRadio.Group
                label="性别"
                name="gender"
                radioType="button"
                options={[{ label: '男', value: 'M' }, { label: '女', value: 'F' }]}
                rules={[{ required: true, message: '请选择性别' }]}
                initialValue="M"
            />
            <ProFormDigit
                label="年龄"
                name="age"
                placeholder="请输入年龄"
                rules={[{ required: true, message: '请输入年龄' }]}
                min={18}
                max={150}
                initialValue={68}
            />
            <ProFormDigit
                label="身高（厘米）"
                name="height"
                placeholder="请输入身高"
                rules={[{ required: true, message: '请输入身高' }]}
                min={100}
                max={250}
                initialValue={183}
            />
            <ProFormDigit
                label="体重（公斤）"
                name="weight"
                placeholder="请输入体重"
                rules={[{ required: true, message: '请输入体重' }]}
                min={20}
                max={200}
                fieldProps={{ precision: 1, step: 1 }}
                initialValue={81}
            />
            <ProFormDigit
                label="身体活动水平"
                name="pla"
                placeholder="请输入体重身体活动水平"
                rules={[{ required: true, message: '请输入体重' }]}
                min={0.5}
                max={3}
                fieldProps={{ precision: 1, step: 1 }}
                initialValue={1.6}
            />
            <ProFormRadio.Group
                label="高脂血症"
                name="tagHTN"
                radioType="button"
                options={[
                    { label: '低危', value: '高脂血症低危' },
                    { label: '中危', value: '高脂血症中危' },
                    { label: '高危', value: '高脂血症高危' }
                ]}
                rules={[{ required: true, message: '请选择高脂血症风险等级' }]}
            />
            <ProFormCheckbox label="高血压" name="tagHTN" />
            <ProFormCheckbox label="糖尿病" name="tagDM" />
            <ProFormCheckbox label="高尿酸血症" name="tagHUA" />
          </ProForm>
        </Card>
        <Card
            title="健康管理方案"
            className="col-span-24 md:col-span-16 shadow-lg"
            loading={false}
        >
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
          <div>1</div>
        </Card>
      </div>
  )
}

export default App
