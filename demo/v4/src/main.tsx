import { StrictMode } from 'react'
import ReactDOM from "react-dom/client"
import {createBrowserRouter, redirect, RouterProvider} from "react-router"

import './index.css'

import App from './App.tsx'


const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
  }
])

ReactDOM.createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <RouterProvider router={router} />
    </StrictMode>
)

export async function protectedLoader() {
  const token = localStorage.getItem('token')
  if (!token) {
    throw redirect("/login")
  }

  return null
}
