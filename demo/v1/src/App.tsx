import * as React from "react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectGroup, SelectItem } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { getStrategy } from "@/utils/llm"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

const tagOptions = [
  {
    type: "主标签",
    tags: [
      { label: "高血脂症", details: ["低危", "中危", "高危"] }
    ]
  },
  {
    type: "合并症标签",
    tags: [
      { label: "高血压", details: ["无高血压", "1级高血压", "2级高血压", "3级高血压"] },
      { label: "糖尿病", details: ["无糖尿病", "1型糖尿病", "2型糖尿病", "糖尿病前期", "糖尿病伴并发症"] },
      { label: "高尿酸血症", details: ["无高尿酸血症", "有高尿酸血症", "有痛风"] },
      { label: "ASCVD", details: ["无ASCVD", "ASCVD超高危人群", "ASCVD极高危人群"] },
      { label: "慢性肾脏病", details: ["无慢性肾脏病", "慢性肾脏病1-2期", "慢性肾脏病3-5期"] },
      { label: "动脉斑块", details: ["无斑块", "有斑块，无动脉狭窄", "有斑块，且伴动脉轻微狭窄", "有斑块，且伴动脉轻度狭窄", "有斑块，且伴动脉中度狭窄", "有斑块，且伴动脉重度狭窄"] }
    ]
  },
  {
    type: "其他标签",
    tags: [
      { label: "月经情况", details: ["绝经", "月经正常", "绝经前期"] },
      { label: "性别", details: ["男性", "女性"] },
      { label: "年龄", details: ["40岁以下", "41-65岁", "65岁以上"] },
      { label: "BMI", details: ["体重过轻", "正常", "超重", "肥胖"] },
      { label: "支付因素", details: ["无商业保险", "有商业保险"] },
      { label: "经济因素", details: ["家庭年收入少于10万", "家庭年收入10-30万", "家庭年收入30-50万", "家庭年收入50-100万元", "家庭年收入100万以上"] },
      { label: "教育水平", details: ["初中及以下", "高中", "大学本科", "研究生", "研究生及以上"] },
      { label: "药物干预", details: ["无药物干预", "调脂药物", "抗血小板药物", "降压药物", "降糖药物"] },
      { label: "家族史", details: ["无家族史", "早发冠心病", "糖尿病", "高血压", "脑血管意外"] },
      { label: "过量饮酒", details: ["无饮酒嗜好", "无过量饮酒", "过量饮酒"] }
    ]
  }
]

function App() {
  // 高血脂症只选一个标签
  const [selectedTags, setSelectedTags] = React.useState<{ [type: string]: string }>({})
  // 合并症和其他标签下每个标签都可以选详细标签
  const [selectedDetails, setSelectedDetails] = React.useState<{ [label: string]: string }>({})

  // 生成方案内容缓存
  const [plan, setPlan] = React.useState<string>("");
  const [loading, setLoading] = React.useState(false);
  const planRef = React.useRef<HTMLDivElement>(null);

  // 计算是否女性
  const isFemale = selectedDetails["性别"] === "女性";

  React.useEffect(() => {
    if (planRef.current) {
      setTimeout(() => {
        if (planRef.current) {
          planRef.current.scrollTop = planRef.current.scrollHeight;
        }
      }, 0);
    }
  }, [plan]);

  return (
    <div className="flex min-h-svh w-full">
      {/* 左侧标签选择区 */}
      <div className="w-[800px] p-4 flex flex-col gap-4">
        {/* 高血脂症分类，单选 */}
        <div className="border rounded-lg p-4 shadow flex flex-col gap-2">
          <div className="flex justify-between items-start mb-1">
            <div className="font-bold text-base">高血脂症</div>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                disabled={loading}
                onClick={() => {
                  // 随机选择高血脂症标签
                  const details = tagOptions[0].tags[0].details;
                  const random = details[Math.floor(Math.random() * details.length)] ?? "";
                  setSelectedTags({ ...selectedTags, ["高血脂症"]: random });

                  // 随机选择合并症标签和其他标签下的详细标签
                  const newDetails: { [label: string]: string } = {};
                  [1, 2].forEach(idx => {
                    tagOptions[idx].tags.forEach(tag => {
                      if (tag.label === "月经情况" && selectedDetails["性别"] !== "女性") {
                        newDetails["月经情况"] = "";
                        return;
                      }
                      if (tag.details.length > 0) {
                        if (Math.random() < 0.5) {
                          newDetails[tag.label] = "";
                        } else {
                          const randomDetail = tag.details[Math.floor(Math.random() * tag.details.length)];
                          newDetails[tag.label] = randomDetail;
                        }
                      } else {
                        newDetails[tag.label] = "";
                      }
                    });
                  });
                  setSelectedDetails(newDetails);
                }}
              >
                随机选择
              </Button>
              <Button
                variant="default"
                size="sm"
                disabled={loading}
                onClick={() => {
                  setPlan('思考中...');
                  setLoading(true);
                  // 生成方案：打印所有选择的type、label和details值
                  const result: any[] = [];
                  // 高血脂症
                  const selected = selectedTags["高血脂症"];
                  if (selected) {
                    result.push({
                      type: "高血脂症",
                      label: selected,
                      detail: null
                    });
                  }
                  // 合并症标签和其他标签
                  [1, 2].forEach(idx => {
                    tagOptions[idx].tags.forEach(tag => {
                      const detail = selectedDetails[tag.label] ?? "";
                      if (detail) {
                        result.push({
                          type: tagOptions[idx].type,
                          label: tag.label,
                          detail
                        });
                      }
                    });
                  });
                  // 获取并输出方案
                  getStrategy(result, (content: string) => {
                    if (content) setPlan(content);
                  }).then((strategy: string) => {
                    setPlan(strategy);
                  }).finally(() => {
                    setLoading(false);
                  });
                }}
              >
                生成方案
              </Button>
            </div>
          </div>
          <Select
            value={selectedTags["高血脂症"] ?? ""}
            onValueChange={(v: string) => setSelectedTags({ ...selectedTags, ["高血脂症"]: v })}
          >
            <SelectTrigger className="w-full min-w-[120px] max-w-full h-8 text-sm">
              <SelectValue placeholder="请选择标签" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {tagOptions[0].tags[0].details.map(detail => (
                  <SelectItem key={detail} value={detail}>{detail}</SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {/* 合并症标签和其他标签，全部平铺 */}
        {[1, 2].map(idx => (
          <div key={tagOptions[idx].type} className="border rounded-lg p-4 shadow flex flex-col gap-2">
            <div className="font-bold text-base mb-1">{tagOptions[idx].type}</div>
            <div className="grid grid-cols-3 gap-3 w-full">
              {tagOptions[idx].tags.map(tag => {
                if (tag.label === "月经情况" && !isFemale) return null;
                return (
                  <div key={tag.label} className="flex flex-col gap-1 min-w-0 w-full">
                    <div className="font-medium text-sm mb-0.5">{tag.label}</div>
                    {tag.details.length > 0 ? (
                      <Select
                        value={selectedDetails[tag.label] ?? ""}
                        onValueChange={(v: string) => setSelectedDetails({ ...selectedDetails, [tag.label]: v })}
                      >
                        <SelectTrigger className="w-full min-w-[120px] max-w-full h-8 text-sm">
                          <SelectValue placeholder="请选择详细标签" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {tag.details.map(dt => (
                              <SelectItem key={dt} value={dt}>{dt}</SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="text-gray-400 text-xs">无详细标签</div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
      {/* 右侧方案展示区 */}
      <div className="flex-1 border-l p-8 flex flex-col h-[calc(100vh-32px)] overflow-y-auto">
        <div className="text-lg font-bold mb-4">生成的方案</div>
        <div ref={planRef} className="bg-gray-50 rounded p-4 text-sm whitespace-pre-wrap flex-1 overflow-auto">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              table: ({node, ...props}) => (
                <table className="min-w-full border border-gray-300 my-2">{props.children}</table>
              ),
              th: ({node, ...props}) => (
                <th className="border border-gray-300 bg-gray-100 px-2 py-1 text-left">{props.children}</th>
              ),
              td: ({node, ...props}) => (
                <td className="border border-gray-300 px-2 py-1">{props.children}</td>
              ),
              tr: ({node, ...props}) => (
                <tr>{props.children}</tr>
              ),
            }}
          >
            {plan ? plan : "请在左侧选择标签并点击生成方案"}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  )
}

export default App
