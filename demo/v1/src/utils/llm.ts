import OpenAI from "openai";

const openai = new OpenAI({
    apiKey: 'sk-e050b9d5559845099260622cfda9b561',
    baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
    dangerouslyAllowBrowser: true,
});

const strategies: Record<string, { nutrition?: string; exercise?: string }> = {
    '高血脂症 - 低危': {
        'nutrition': '平衡膳食为主，防超重',
        'exercise': '每周150分钟中等强度有氧运动'
    },
    '高血脂症 - 中危': {
        'nutrition': '控油限盐、增加DASH/地中海饮食模式、注意膳食胆固醇摄入',
        'exercise': '建议中等+抗阻结合（如快走+自重训练）'
    },
    '高血脂症 - 高危': {
        'nutrition': '精细化营养处方（如脂肪型控制+高纤维+低GI），需结合个体情况调控能量/脂肪酸种类',
        'exercise': '在医师评估后开展有氧+抗阻+柔韧训练；起始强度需个体化评估，如心率不超过最大心率的50%–60%'
    },
    '合并症标签 - 高血压 - 1级高血压': {
        'nutrition': '基于高血脂管理，再叠加控盐（<5g/天），可引入DASH饮食模型：高钾、低钠、高纤、低脂',
        'exercise': '运动频率建议：5–7天/周; 类型：有氧为主，建议结合抗阻训练; 强度：中等强度（最大心率50–70%）'
    },
    '合并症标签 - 高血压 - 2级高血压': {
        'nutrition': '重点为DASH或DASH-Mediterranean复合饮食; 控盐更严格：推荐<4g/天; 避免含咖啡因、升压食品（如腌制品、浓汤等）; 引入功能性食品（植物甾醇、大豆蛋白）',
        'exercise': '需经医生评估后逐步开始; 以低强度到中等强度有氧运动为主（如快走）; 抗阻训练需谨慎，避免憋气用力（Valsalva效应）; 强调运动中/后血压监测; RPE不超过13，建议佩戴心率监测设备'
    },
    '合并症标签 - 高血压 - 3级高血压': {
        'nutrition': '必须低钠低脂，控制能量、防体重增加; 必须限制钠盐至<3g/天; 小量多餐，易消化，避免血压波动; 控制液体总摄入（若有心功能异常）',
        'exercise': '必须在医生/运动康复师评估后开展; 初期以散步、舒缓拉伸、呼吸练习为主; 避免任何负重训练或高强度训练'
    },
    '合并症标签 - 糖尿病 - 1型糖尿病': {
        'nutrition': '高纤维、低GI、稳定碳水化合物摄入；按胰岛素调整进食时间',
        'exercise': '需结合血糖动态监测安排运动；优先中等强度有氧，运动前后监测血糖'
    },
    '合并症标签 - 糖尿病 - 2型糖尿病': {
        'nutrition': '地中海饮食或DASH，控制总能量摄入，减少精制碳水',
        'exercise': '每周150–300分钟中等强度有氧运动 + 抗阻训练；鼓励日常活动增加'
    },
    '合并症标签 - 糖尿病 - 糖尿病前期': {
        'nutrition': '控能量、低GI饮食、提高膳食纤维摄入',
        'exercise': '建议每天30–60分钟有氧运动，配合抗阻训练提高胰岛素敏感性'
    },
    '合并症标签 - 糖尿病 - 糖尿病伴并发症': {
        'nutrition': '严控糖摄入、限制脂肪种类、控制钠盐摄入',
        'exercise': '依据并发症类型（如视网膜病、神经病变等）调整运动形式和强度'
    },
    '合并症标签 - 高尿酸血症 - 有高尿酸血症': {
        'nutrition': '限嘌呤、限酒精、高钾低钠饮食；多饮水、避免高果糖食物',
        'exercise': '中等强度有氧为主，避免剧烈运动诱发血尿酸升高'
    },
    '合并症标签 - 高尿酸血症 - 有痛风': {
        'nutrition': '同高尿酸饮食 + 控制总能量摄入',
        'exercise': '痛风缓解期运动；急性期避免运动；关注关节功能'
    },
    '合并症标签 - ASCVD - ASCVD超高危人群': {
        'nutrition': '限胆固醇+低脂膳食，DASH/地中海饮食，避免诱发血压/血糖波动食物',
        'exercise': '医生评估后个体化制定，低至中等强度逐步进行，重点提升心肺耐力'
    },
    '合并症标签 - ASCVD - ASCVD极高危人群': {
        'nutrition': '低脂、低盐、低糖，适度蛋白质摄入，强化植物性食物',
        'exercise': '运动需在康复医师指导下进行，起始低强度，严密监测'
    },
    '合并症标签 - 慢性肾脏病 - 慢性肾脏病1-2期': {
        'nutrition': '控磷钾、适度限蛋白、低盐、避免加工食品',
        'exercise': '避免高强度运动，建议中等强度步行、骑车等'
    },
    '合并症标签 - 慢性肾脏病 - 慢性肾脏病3-5期': {
        'nutrition': '低蛋白低钠饮食、控制钾磷；个体化膳食管理',
        'exercise': '需医生批准，轻量有氧、拉伸、功能锻炼为主'
    },
    '合并症标签 - 动脉斑块 - 有斑块，无动脉狭窄': {
        'nutrition': '高纤低脂饮食、富含抗氧化食物、限盐',
        'exercise': '有氧训练为主，控制体重和代谢参数'
    },
    '合并症标签 - 动脉斑块 - 有斑块，且伴动脉轻微狭窄': {
        'nutrition': '控胆固醇、摄入优质脂肪酸、低糖低钠',
        'exercise': '有氧为主，适度强度，严密监测运动心率反应'
    },
    '合并症标签 - 动脉斑块 - 有斑块，且伴动脉轻度狭窄': {
        'nutrition': '严格低脂、限钠限糖膳食，强调Omega-3摄入',
        'exercise': '低中强度有氧，注意个体耐受性，配合康复训练'
    },
    '合并症标签 - 动脉斑块 - 有斑块，且伴动脉中度狭窄': {
        'nutrition': '地中海+低盐饮食，控制三餐血糖血脂波动',
        'exercise': '医生指导下进行，低强度步行、太极等安全性高的训练'
    },
    '合并症标签 - 动脉斑块 - 有斑块，且伴动脉重度狭窄': {
        'nutrition': '个体化饮食，低脂低盐极限化管理，必要时补充辅助营养剂',
        'exercise': '不建议剧烈运动，仅在心脏康复指导下进行'
    },
    '其他标签 - 月经情况 - 绝经': {
        'nutrition': '高钙高维D饮食，适量优质蛋白，控制总脂摄入',
        'exercise': '有氧+抗阻结合，强调平衡与抗骨质疏松训练'
    },
    '其他标签 - 月经情况 - 月经正常': {
        'nutrition': '适量铁摄入、控制糖脂比例、均衡微量营养素',
        'exercise': '结合经期节律调整运动强度；中等强度有氧为主'
    },
    '其他标签 - 月经情况 - 绝经前期': {
        'nutrition': '增加钙镁摄入、避免高脂高糖饮食',
        'exercise': '建议中低强度运动，重视核心和下肢力量训练'
    },
    '其他标签 - 性别 - 男性': {
        'nutrition': '控总能量、避免啤酒肚饮食模式',
        'exercise': '建议结合抗阻训练塑形+中高强度有氧'
    },
    '其他标签 - 年龄 - 40岁以下': {
        'nutrition': '强化健康饮食习惯，控制外卖、甜饮',
        'exercise': '鼓励多样运动，形成长期锻炼习惯'
    },
    '其他标签 - 年龄 - 41-65岁': {
        'nutrition': '地中海+DASH膳食模式结合',
        'exercise': '中等强度有氧+抗阻配合，每周≥4天'
    },
    '其他标签 - 年龄 - 65岁以上': {
        'nutrition': '控脂饮食基础上加强蛋白摄入与维生素D',
        'exercise': '低强度有氧+抗阻结合，重视肌力、平衡与耐力'
    },
    '合并症标签 - BMI - 体重过轻': {
        'nutrition': '高能量密度食物、健康脂肪补充、避免过多膳食纤维抑制吸收',
        'exercise': '初期轻量运动为主，强化肌肉生长'
    },
    '合并症标签 - BMI - 正常': {
        'nutrition': '平衡膳食，维持热量摄入与能量支出平衡',
        'exercise': '有氧+抗阻结合维持体适能'
    },
    '合并症标签 - BMI - 超重': {
        'nutrition': '控能量饮食、限制精制糖脂、鼓励饱腹感食物',
        'exercise': '以脂肪燃烧为目标的中等强度运动为主'
    },
    '合并症标签 - BMI - 肥胖': {
        'nutrition': '控制每日热量赤字500–800kcal，使用餐盘法、行为干预配合',
        'exercise': '每周至少300分钟中等强度有氧+渐进抗阻训练'
    },
    '其他标签 - 药物干预 - 调脂药物': {
        'nutrition': '避免与药物代谢冲突食物，如西柚；保持低脂饮食',
        'exercise': '有氧+抗阻结合，提升HDL'
    },
    '其他标签 - 药物干预 - 抗血小板药物': {
        'nutrition': '避免出血风险增加的补品或高剂量维生素E',
        'exercise': '避免跌倒风险大的运动，关注安全性'
    },
    '其他标签 - 药物干预 - 降压药物': {
        'nutrition': '稳定钠钾摄入，避免脱水',
        'exercise': '运动中注意姿位性低血压，避免剧烈起伏'
    },
    '其他标签 - 药物干预 - 降糖药物': {
        'nutrition': '控糖同时均衡碳水分布，关注GI值',
        'exercise': '饭后运动优先，随身携带糖源防低血糖'
    },
    '其他标签 - 家族史 - 早发冠心病': {
        'nutrition': '更加严格控制脂肪和胆固醇摄入',
        'exercise': '有氧运动贯穿，必要时评估心肺适应性'
    },
    '其他标签 - 家族史 - 糖尿病': {
        'nutrition': '限脂限盐限糖，抗氧化食物摄入',
        'exercise': '增强有氧运动，监测体重腰围'
    },
    '其他标签 - 家族史 - 高血压': {
        'nutrition': '控盐控脂并重，推荐钾钙镁丰富食物',
        'exercise': '有氧运动占主导，运动中关注血压反应'
    },
    '其他标签 - 家族史 - 脑血管意外': {
        'nutrition': '限脂限盐限糖，抗氧化食物摄入',
        'exercise': '运动安全性评估后开展，节律性为佳'
    },
    '其他标签 - 过量饮酒 - 无过量饮酒': {
        'nutrition': '控制饮酒频率与量'
    },
    '其他标签 - 过量饮酒 - 过量饮酒': {
        'nutrition': '减酒为前提，补充B族维生素、限制高嘌呤食物'
    }
}

export const getStrategy = async (result: any[], onStream?: (content: string) => void): Promise<string> => {
    const nutritions: string[] = [];
    const exercises: string[] = [];
    result.forEach(item => {
        const key = `${item.type} - ${item.label}${item.detail ? ` - ${item.detail}` : ''}`;
        if (strategies[key]) {
            if (strategies[key].nutrition) {
                nutritions.push(key + '：' + strategies[key].nutrition);
            }
            if (strategies[key].exercise) {
                exercises.push(key + '：' + strategies[key].exercise); 
            }
        }
    });
    const completion = await openai.chat.completions.create({
        model: "qwen3-8b",
        messages: [
            {"role": "system", "content": "你是一名专业的营养师，根据用户提供的营养建议和运动建议，给出一份详细的饮食和运动计划。"},
            {"role": "user", "content": getTemplate(nutritions.map(n => '- ' + n).join('\n'), exercises.map(e => '- ' + e).join('\n'))}
        ],
        stream: true,
    });
    
    let fullContent = "";
    for await (const chunk of completion) {
        if (Array.isArray(chunk.choices) && chunk.choices.length > 0) {
            fullContent = fullContent + (chunk.choices[0].delta.content || "");
            if (onStream) onStream(fullContent);
        }
    }
    return (fullContent ? ('\n' + fullContent) : '');
}

const getTemplate = (nutritions: string, exercises: string) => {
    const template = `请根据以下的营养和运动建议，给出一份详细的饮食和运动计划（周一至周日）。
        营养建议：
        ${nutritions}
        运动建议：
        ${exercises}`
    console.log(template);
    return template
}   